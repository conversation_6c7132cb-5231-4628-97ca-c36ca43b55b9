import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { store, key } from './store'
import { createPinia } from 'pinia'
import { useAuthStore } from './stores/auth'
import register from './components/jt-global-register'
import i18n from './i18n'

// global css
import './assets/styles/index.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// cesium vue
import cesiumVue from './libs/cesium/cesium-vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// const  currentEnv=import.meta.env.VITE_APP_ENV;
// if(currentEnv==='production'){
//   window.DIGITALFARMLANDURL=''
// }
(window as any).resourceBaseUrl='.'

// 创建 Pinia 实例
const pinia = createPinia()

app.use(store, key).use(pinia).use(router).use(cesiumVue).use(i18n)

register(app)
app.use(ElementPlus)

// 初始化认证状态
const authStore = useAuthStore()
authStore.initAuth().finally(() => {
  app.mount('#app')
})
