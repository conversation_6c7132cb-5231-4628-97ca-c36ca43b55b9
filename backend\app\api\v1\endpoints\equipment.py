from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.core.database import get_db
from app.models.equipment import Equipment as EquipmentModel
from app.schemas.equipment import (
    Equipment, EquipmentCreate, EquipmentUpdate, 
    EquipmentList, EquipmentStatus
)

router = APIRouter()

@router.get("/", response_model=EquipmentList)
def get_equipment_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="设备状态筛选"),
    is_active: Optional[bool] = Query(None, description="是否激活筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db)
):
    """获取设备列表"""
    query = db.query(EquipmentModel)
    
    # 应用筛选条件
    if status:
        query = query.filter(EquipmentModel.status == status)
    if is_active is not None:
        query = query.filter(EquipmentModel.is_active == is_active)
    if search:
        query = query.filter(
            EquipmentModel.name.contains(search) |
            EquipmentModel.equipment_id.contains(search)
        )
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.offset(offset).limit(size).all()
    
    # 计算总页数
    pages = (total + size - 1) // size
    
    return EquipmentList(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/status", response_model=EquipmentStatus)
def get_equipment_status(db: Session = Depends(get_db)):
    """获取设备状态统计"""
    total = db.query(EquipmentModel).count()
    active = db.query(EquipmentModel).filter(EquipmentModel.is_active == True).count()
    inactive = total - active
    online = db.query(EquipmentModel).filter(EquipmentModel.status == "运行中").count()
    offline = db.query(EquipmentModel).filter(EquipmentModel.status == "离线").count()
    low_battery = db.query(EquipmentModel).filter(EquipmentModel.battery_level < 20).count()
    
    # 需要维护的设备（这里简化为电量低于30%的设备）
    maintenance_due = db.query(EquipmentModel).filter(EquipmentModel.battery_level < 30).count()
    
    return EquipmentStatus(
        total=total,
        active=active,
        inactive=inactive,
        online=online,
        offline=offline,
        low_battery=low_battery,
        maintenance_due=maintenance_due
    )

@router.get("/{equipment_id}", response_model=Equipment)
def get_equipment(equipment_id: int, db: Session = Depends(get_db)):
    """获取单个设备信息"""
    equipment = db.query(EquipmentModel).filter(EquipmentModel.id == equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="设备不存在")
    return equipment

@router.post("/", response_model=Equipment)
def create_equipment(equipment: EquipmentCreate, db: Session = Depends(get_db)):
    """创建新设备"""
    # 检查设备ID是否已存在
    existing = db.query(EquipmentModel).filter(EquipmentModel.equipment_id == equipment.equipment_id).first()
    if existing:
        raise HTTPException(status_code=400, detail="设备ID已存在")
    
    db_equipment = EquipmentModel(**equipment.dict())
    db.add(db_equipment)
    db.commit()
    db.refresh(db_equipment)
    return db_equipment

@router.put("/{equipment_id}", response_model=Equipment)
def update_equipment(
    equipment_id: int, 
    equipment_update: EquipmentUpdate, 
    db: Session = Depends(get_db)
):
    """更新设备信息"""
    equipment = db.query(EquipmentModel).filter(EquipmentModel.id == equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    update_data = equipment_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(equipment, field, value)
    
    db.commit()
    db.refresh(equipment)
    return equipment

@router.delete("/{equipment_id}")
def delete_equipment(equipment_id: int, db: Session = Depends(get_db)):
    """删除设备"""
    equipment = db.query(EquipmentModel).filter(EquipmentModel.id == equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    db.delete(equipment)
    db.commit()
    return {"message": "设备删除成功"}
