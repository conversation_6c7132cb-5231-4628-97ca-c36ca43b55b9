from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

class EquipmentBase(BaseModel):
    """设备基础模型"""
    name: str = Field(..., description="设备名称")
    equipment_id: str = Field(..., description="设备唯一标识")
    longitude: float = Field(..., description="经度")
    latitude: float = Field(..., description="纬度")
    altitude: Optional[float] = Field(0.0, description="海拔高度")
    status: Optional[str] = Field("运行中", description="设备状态")
    battery_level: Optional[float] = Field(100.0, description="电池电量百分比")
    signal_strength: Optional[float] = Field(100.0, description="信号强度")
    model: Optional[str] = Field(None, description="设备型号")
    manufacturer: Optional[str] = Field(None, description="制造商")
    firmware_version: Optional[str] = Field(None, description="固件版本")
    installation_date: Optional[datetime] = Field(None, description="安装日期")
    last_maintenance: Optional[datetime] = Field(None, description="上次维护时间")
    next_maintenance: Optional[datetime] = Field(None, description="下次维护时间")
    maintenance_notes: Optional[str] = Field(None, description="维护备注")
    is_active: Optional[bool] = Field(True, description="是否激活")

class EquipmentCreate(EquipmentBase):
    """创建设备模型"""
    pass

class EquipmentUpdate(BaseModel):
    """更新设备模型"""
    name: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None
    altitude: Optional[float] = None
    status: Optional[str] = None
    battery_level: Optional[float] = None
    signal_strength: Optional[float] = None
    model: Optional[str] = None
    manufacturer: Optional[str] = None
    firmware_version: Optional[str] = None
    installation_date: Optional[datetime] = None
    last_maintenance: Optional[datetime] = None
    next_maintenance: Optional[datetime] = None
    maintenance_notes: Optional[str] = None
    is_active: Optional[bool] = None

class Equipment(EquipmentBase):
    """设备响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EquipmentList(BaseModel):
    """设备列表响应模型"""
    items: List[Equipment]
    total: int
    page: int
    size: int
    pages: int

class EquipmentStatus(BaseModel):
    """设备状态统计模型"""
    total: int
    active: int
    inactive: int
    online: int
    offline: int
    low_battery: int
    maintenance_due: int
