#!/usr/bin/env python3
"""
启动脚本
用于启动农业监测系统后端服务
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要 Python 3.8 或更高版本")
        sys.exit(1)
    print(f"✓ Python 版本: {sys.version}")

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True, cwd=Path(__file__).parent)
        print("✓ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"错误: 安装依赖包失败 - {e}")
        sys.exit(1)

def setup_environment():
    """设置环境"""
    env_file = Path(__file__).parent / ".env"
    env_example = Path(__file__).parent / ".env.example"

    if not env_file.exists() and env_example.exists():
        print("正在创建环境配置文件...")
        env_file.write_text(env_example.read_text(encoding='utf-8'), encoding='utf-8')
        print("✓ 已创建 .env 文件，请根据需要修改配置")

def init_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        script_path = Path(__file__).parent / "scripts" / "init_data.py"
        subprocess.run([sys.executable, str(script_path)],
                      check=True, cwd=Path(__file__).parent)
        print("✓ 数据库初始化完成")
    except subprocess.CalledProcessError as e:
        print(f"警告: 数据库初始化失败 - {e}")
        print("可能数据库已存在数据，继续启动服务...")

def start_server(host="0.0.0.0", port=8000, reload=True):
    """启动服务器"""
    print(f"正在启动服务器 http://{host}:{port}")
    print("API 文档地址:")
    print(f"  - Swagger UI: http://{host}:{port}/api/v1/docs")
    print(f"  - ReDoc: http://{host}:{port}/api/v1/redoc")
    print("\n按 Ctrl+C 停止服务器\n")

    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except ImportError:
        print("错误: 未找到 uvicorn，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "uvicorn[standard]"], check=True)
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="农业监测系统后端启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--no-reload", action="store_true", help="禁用自动重载")
    parser.add_argument("--skip-install", action="store_true", help="跳过依赖安装")
    parser.add_argument("--skip-init", action="store_true", help="跳过数据库初始化")

    args = parser.parse_args()

    print("=" * 50)
    print("农业监测系统后端启动脚本")
    print("=" * 50)

    # 检查Python版本
    check_python_version()

    # 切换到脚本目录
    os.chdir(Path(__file__).parent)

    # 安装依赖
    if not args.skip_install:
        install_dependencies()

    # 设置环境
    setup_environment()

    # 初始化数据库
    if not args.skip_init:
        init_database()

    # 启动服务器
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload
    )

if __name__ == "__main__":
    main()
