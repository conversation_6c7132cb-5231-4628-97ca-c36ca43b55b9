import apiClient from './index'
import type { 
  TerrainData, 
  TerrainDataCreate, 
  TerrainAnalysis,
  PaginatedResponse 
} from './types'

export const terrainApi = {
  // 获取地形数据列表
  getTerrainDataList: async (params?: {
    page?: number
    size?: number
    sample_type?: string
    created_by?: string
    search?: string
  }): Promise<PaginatedResponse<TerrainData>> => {
    const response = await apiClient.get('/api/v1/terrain-data/', { params })
    return response.data
  },

  // 获取单个地形数据
  getTerrainData: async (id: number): Promise<TerrainData> => {
    const response = await apiClient.get(`/api/v1/terrain-data/${id}`)
    return response.data
  },

  // 获取地形分析结果
  getTerrainAnalysis: async (id: number): Promise<TerrainAnalysis> => {
    const response = await apiClient.get(`/api/v1/terrain-data/${id}/analysis`)
    return response.data
  },

  // 创建地形数据
  createTerrainData: async (data: TerrainDataCreate): Promise<TerrainData> => {
    const response = await apiClient.post('/api/v1/terrain-data/', data)
    return response.data
  },

  // 更新地形数据
  updateTerrainData: async (id: number, data: Partial<TerrainDataCreate>): Promise<TerrainData> => {
    const response = await apiClient.put(`/api/v1/terrain-data/${id}`, data)
    return response.data
  },

  // 删除地形数据
  deleteTerrainData: async (id: number): Promise<void> => {
    await apiClient.delete(`/api/v1/terrain-data/${id}`)
  },

  // 导出地形数据
  exportTerrainData: async (params?: {
    sample_type?: string
    created_by?: string
    search?: string
  }): Promise<Blob> => {
    const response = await apiClient.get('/api/v1/terrain-data/export', {
      params,
      responseType: 'blob',
    })
    return response.data
  },
}
