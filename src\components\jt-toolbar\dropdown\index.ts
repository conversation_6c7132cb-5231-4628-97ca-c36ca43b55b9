import cameraPercentageChangeRate from './camera-percentage-change-rate/index.vue'
import earthSurfaceColorPicker from './earth-surface-color-picker/index.vue'
import elevationContourSetting from './elevation-contour-setting/index.vue'
import earthLightSetting from './earth-light-setting/index.vue'
import imagerySplitPosition from './imagery-split-position/index.vue'
import selectViewMode from './select-view-mode/index.vue'

export default {
  cameraPercentageChangeRate,
  earthSurfaceColorPicker,
  elevationContourSetting,
  earthLightSetting,
  imagerySplitPosition,
  selectViewMode,
}
