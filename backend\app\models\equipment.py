from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class Equipment(Base):
    """设备监控模型"""
    __tablename__ = "equipment"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="设备名称")
    equipment_id = Column(String(50), unique=True, nullable=False, comment="设备唯一标识")

    # 位置信息
    longitude = Column(Float, nullable=False, comment="经度")
    latitude = Column(Float, nullable=False, comment="纬度")
    altitude = Column(Float, default=0.0, comment="海拔高度")

    # 设备状态
    status = Column(String(20), default="运行中", comment="设备状态")
    battery_level = Column(Float, default=100.0, comment="电池电量百分比")
    signal_strength = Column(Float, default=100.0, comment="信号强度")

    # 设备信息
    model = Column(String(100), comment="设备型号")
    manufacturer = Column(String(100), comment="制造商")
    firmware_version = Column(String(50), comment="固件版本")
    installation_date = Column(DateTime, comment="安装日期")

    # 维护信息
    last_maintenance = Column(DateTime, comment="上次维护时间")
    next_maintenance = Column(DateTime, comment="下次维护时间")
    maintenance_notes = Column(Text, comment="维护备注")

    # 系统字段
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    monitoring_data = relationship("MonitoringData", back_populates="equipment")

    def __repr__(self):
        return f"<Equipment(id={self.id}, name='{self.name}', status='{self.status}')>"
