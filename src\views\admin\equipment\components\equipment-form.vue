<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    :disabled="readonly"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入设备名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备ID" prop="equipment_id">
          <el-input v-model="formData.equipment_id" placeholder="请输入设备唯一标识" />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="经度" prop="longitude">
          <el-input-number
            v-model="formData.longitude"
            :precision="6"
            :step="0.000001"
            :min="-180"
            :max="180"
            style="width: 100%"
            placeholder="请输入经度"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="纬度" prop="latitude">
          <el-input-number
            v-model="formData.latitude"
            :precision="6"
            :step="0.000001"
            :min="-90"
            :max="90"
            style="width: 100%"
            placeholder="请输入纬度"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="海拔高度" prop="altitude">
          <el-input-number
            v-model="formData.altitude"
            :precision="1"
            :step="0.1"
            style="width: 100%"
            placeholder="请输入海拔高度(米)"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择设备状态" style="width: 100%">
            <el-option label="运行中" value="运行中" />
            <el-option label="离线" value="离线" />
            <el-option label="维护中" value="维护中" />
            <el-option label="低电量" value="低电量" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="电池电量" prop="battery_level">
          <el-input-number
            v-model="formData.battery_level"
            :precision="1"
            :step="1"
            :min="0"
            :max="100"
            style="width: 100%"
            placeholder="电池电量百分比"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="信号强度" prop="signal_strength">
          <el-input-number
            v-model="formData.signal_strength"
            :precision="1"
            :step="1"
            :min="0"
            :max="100"
            style="width: 100%"
            placeholder="信号强度百分比"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="设备型号" prop="model">
          <el-input v-model="formData.model" placeholder="请输入设备型号" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="制造商" prop="manufacturer">
          <el-input v-model="formData.manufacturer" placeholder="请输入制造商" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="固件版本" prop="firmware_version">
          <el-input v-model="formData.firmware_version" placeholder="请输入固件版本" />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="安装日期" prop="installation_date">
          <el-date-picker
            v-model="formData.installation_date"
            type="datetime"
            placeholder="请选择安装日期"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="上次维护" prop="last_maintenance">
          <el-date-picker
            v-model="formData.last_maintenance"
            type="datetime"
            placeholder="请选择上次维护时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="下次维护" prop="next_maintenance">
          <el-date-picker
            v-model="formData.next_maintenance"
            type="datetime"
            placeholder="请选择下次维护时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row>
      <el-col :span="24">
        <el-form-item label="维护备注" prop="maintenance_notes">
          <el-input
            v-model="formData.maintenance_notes"
            type="textarea"
            :rows="3"
            placeholder="请输入维护备注"
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row>
      <el-col :span="24">
        <el-form-item label="激活状态" prop="is_active">
          <el-switch
            v-model="formData.is_active"
            active-text="已激活"
            inactive-text="未激活"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { Equipment, EquipmentCreate, EquipmentUpdate } from '@/api/types'

interface Props {
  equipment?: Equipment | null
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  equipment: null,
  readonly: false
})

const emit = defineEmits<{
  submit: [data: EquipmentCreate | EquipmentUpdate]
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive<EquipmentCreate>({
  name: '',
  equipment_id: '',
  longitude: 0,
  latitude: 0,
  altitude: 0,
  status: '运行中',
  battery_level: 100,
  signal_strength: 100,
  model: '',
  manufacturer: '',
  firmware_version: '',
  installation_date: '',
  last_maintenance: '',
  next_maintenance: '',
  maintenance_notes: '',
  is_active: true
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '设备名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  equipment_id: [
    { required: true, message: '请输入设备ID', trigger: 'blur' },
    { min: 3, max: 50, message: '设备ID长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '设备ID只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  longitude: [
    { required: true, message: '请输入经度', trigger: 'blur' },
    { type: 'number', min: -180, max: 180, message: '经度范围在 -180 到 180 之间', trigger: 'blur' }
  ],
  latitude: [
    { required: true, message: '请输入纬度', trigger: 'blur' },
    { type: 'number', min: -90, max: 90, message: '纬度范围在 -90 到 90 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择设备状态', trigger: 'change' }
  ],
  battery_level: [
    { type: 'number', min: 0, max: 100, message: '电池电量范围在 0 到 100 之间', trigger: 'blur' }
  ],
  signal_strength: [
    { type: 'number', min: 0, max: 100, message: '信号强度范围在 0 到 100 之间', trigger: 'blur' }
  ]
}

// 监听设备数据变化
watch(
  () => props.equipment,
  (newEquipment) => {
    if (newEquipment) {
      // 编辑模式，填充表单数据
      Object.assign(formData, {
        name: newEquipment.name,
        equipment_id: newEquipment.equipment_id,
        longitude: newEquipment.longitude,
        latitude: newEquipment.latitude,
        altitude: newEquipment.altitude || 0,
        status: newEquipment.status,
        battery_level: newEquipment.battery_level || 100,
        signal_strength: newEquipment.signal_strength || 100,
        model: newEquipment.model || '',
        manufacturer: newEquipment.manufacturer || '',
        firmware_version: newEquipment.firmware_version || '',
        installation_date: newEquipment.installation_date || '',
        last_maintenance: newEquipment.last_maintenance || '',
        next_maintenance: newEquipment.next_maintenance || '',
        maintenance_notes: newEquipment.maintenance_notes || '',
        is_active: newEquipment.is_active
      })
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    equipment_id: '',
    longitude: 0,
    latitude: 0,
    altitude: 0,
    status: '运行中',
    battery_level: 100,
    signal_strength: 100,
    model: '',
    manufacturer: '',
    firmware_version: '',
    installation_date: '',
    last_maintenance: '',
    next_maintenance: '',
    maintenance_notes: '',
    is_active: true
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 验证表单
const validate = async () => {
  if (!formRef.value) throw new Error('表单引用不存在')
  
  try {
    await formRef.value.validate()
    
    // 处理空字符串为 undefined
    const submitData = { ...formData }
    Object.keys(submitData).forEach(key => {
      if (submitData[key as keyof typeof submitData] === '') {
        submitData[key as keyof typeof submitData] = undefined as any
      }
    })
    
    return submitData
  } catch (error) {
    ElMessage.error('请检查表单输入')
    throw error
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
