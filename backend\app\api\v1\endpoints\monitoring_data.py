from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc

from app.core.database import get_db
from app.models.monitoring_data import MonitoringData as MonitoringDataModel
from app.models.equipment import Equipment as EquipmentModel
from app.schemas.monitoring_data import (
    MonitoringData, MonitoringDataCreate, MonitoringDataUpdate,
    MonitoringDataList, MonitoringDataStats
)

router = APIRouter()

@router.get("/", response_model=MonitoringDataList)
def get_monitoring_data_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    equipment_id: Optional[int] = Query(None, description="设备ID筛选"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间"),
    data_quality: Optional[str] = Query(None, description="数据质量筛选"),
    db: Session = Depends(get_db)
):
    """获取监测数据列表"""
    query = db.query(MonitoringDataModel)
    
    # 应用筛选条件
    if equipment_id:
        query = query.filter(MonitoringDataModel.equipment_id == equipment_id)
    if start_time:
        query = query.filter(MonitoringDataModel.measured_at >= start_time)
    if end_time:
        query = query.filter(MonitoringDataModel.measured_at <= end_time)
    if data_quality:
        query = query.filter(MonitoringDataModel.data_quality == data_quality)
    
    # 按测量时间倒序排列
    query = query.order_by(desc(MonitoringDataModel.measured_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.offset(offset).limit(size).all()
    
    # 计算总页数
    pages = (total + size - 1) // size
    
    return MonitoringDataList(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/latest", response_model=List[MonitoringData])
def get_latest_monitoring_data(
    equipment_ids: Optional[str] = Query(None, description="设备ID列表，逗号分隔"),
    db: Session = Depends(get_db)
):
    """获取最新监测数据"""
    if equipment_ids:
        equipment_id_list = [int(id.strip()) for id in equipment_ids.split(",")]
        
        # 为每个设备获取最新数据
        latest_data = []
        for eq_id in equipment_id_list:
            latest = db.query(MonitoringDataModel)\
                .filter(MonitoringDataModel.equipment_id == eq_id)\
                .order_by(desc(MonitoringDataModel.measured_at))\
                .first()
            if latest:
                latest_data.append(latest)
        return latest_data
    else:
        # 获取所有设备的最新数据
        subquery = db.query(
            MonitoringDataModel.equipment_id,
            func.max(MonitoringDataModel.measured_at).label('max_time')
        ).group_by(MonitoringDataModel.equipment_id).subquery()
        
        latest_data = db.query(MonitoringDataModel)\
            .join(subquery, and_(
                MonitoringDataModel.equipment_id == subquery.c.equipment_id,
                MonitoringDataModel.measured_at == subquery.c.max_time
            )).all()
        
        return latest_data

@router.get("/stats/{equipment_id}", response_model=MonitoringDataStats)
def get_monitoring_data_stats(
    equipment_id: int,
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    db: Session = Depends(get_db)
):
    """获取设备监测数据统计"""
    # 检查设备是否存在
    equipment = db.query(EquipmentModel).filter(EquipmentModel.id == equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    # 获取最新数据
    latest_data = db.query(MonitoringDataModel)\
        .filter(MonitoringDataModel.equipment_id == equipment_id)\
        .order_by(desc(MonitoringDataModel.measured_at))\
        .first()
    
    # 计算平均值
    stats_query = db.query(
        func.avg(MonitoringDataModel.soil_temperature).label('avg_soil_temperature'),
        func.avg(MonitoringDataModel.soil_moisture).label('avg_soil_moisture'),
        func.avg(MonitoringDataModel.ambient_temperature).label('avg_ambient_temperature'),
        func.avg(MonitoringDataModel.ambient_humidity).label('avg_ambient_humidity'),
        func.avg(MonitoringDataModel.light_intensity).label('avg_light_intensity'),
        func.count(MonitoringDataModel.id).label('data_count'),
        func.max(MonitoringDataModel.measured_at).label('last_update')
    ).filter(
        and_(
            MonitoringDataModel.equipment_id == equipment_id,
            MonitoringDataModel.measured_at >= start_time,
            MonitoringDataModel.measured_at <= end_time
        )
    ).first()
    
    return MonitoringDataStats(
        equipment_id=equipment_id,
        latest_data=latest_data,
        avg_soil_temperature=stats_query.avg_soil_temperature,
        avg_soil_moisture=stats_query.avg_soil_moisture,
        avg_ambient_temperature=stats_query.avg_ambient_temperature,
        avg_ambient_humidity=stats_query.avg_ambient_humidity,
        avg_light_intensity=stats_query.avg_light_intensity,
        data_count=stats_query.data_count or 0,
        last_update=stats_query.last_update
    )

@router.get("/{data_id}", response_model=MonitoringData)
def get_monitoring_data(data_id: int, db: Session = Depends(get_db)):
    """获取单条监测数据"""
    data = db.query(MonitoringDataModel).filter(MonitoringDataModel.id == data_id).first()
    if not data:
        raise HTTPException(status_code=404, detail="监测数据不存在")
    return data

@router.post("/", response_model=MonitoringData)
def create_monitoring_data(data: MonitoringDataCreate, db: Session = Depends(get_db)):
    """创建监测数据"""
    # 检查设备是否存在
    equipment = db.query(EquipmentModel).filter(EquipmentModel.id == data.equipment_id).first()
    if not equipment:
        raise HTTPException(status_code=404, detail="设备不存在")
    
    db_data = MonitoringDataModel(**data.dict())
    db.add(db_data)
    db.commit()
    db.refresh(db_data)
    return db_data

@router.post("/batch", response_model=List[MonitoringData])
def create_monitoring_data_batch(data_list: List[MonitoringDataCreate], db: Session = Depends(get_db)):
    """批量创建监测数据"""
    db_data_list = []
    for data in data_list:
        # 检查设备是否存在
        equipment = db.query(EquipmentModel).filter(EquipmentModel.id == data.equipment_id).first()
        if not equipment:
            raise HTTPException(status_code=404, detail=f"设备ID {data.equipment_id} 不存在")
        
        db_data = MonitoringDataModel(**data.dict())
        db_data_list.append(db_data)
    
    db.add_all(db_data_list)
    db.commit()
    
    for db_data in db_data_list:
        db.refresh(db_data)
    
    return db_data_list

@router.put("/{data_id}", response_model=MonitoringData)
def update_monitoring_data(
    data_id: int,
    data_update: MonitoringDataUpdate,
    db: Session = Depends(get_db)
):
    """更新监测数据"""
    data = db.query(MonitoringDataModel).filter(MonitoringDataModel.id == data_id).first()
    if not data:
        raise HTTPException(status_code=404, detail="监测数据不存在")
    
    update_data = data_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(data, field, value)
    
    db.commit()
    db.refresh(data)
    return data

@router.delete("/{data_id}")
def delete_monitoring_data(data_id: int, db: Session = Depends(get_db)):
    """删除监测数据"""
    data = db.query(MonitoringDataModel).filter(MonitoringDataModel.id == data_id).first()
    if not data:
        raise HTTPException(status_code=404, detail="监测数据不存在")
    
    db.delete(data)
    db.commit()
    return {"message": "监测数据删除成功"}
