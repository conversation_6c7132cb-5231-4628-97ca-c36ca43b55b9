from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON
from sqlalchemy.sql import func
from app.core.database import Base

class TerrainData(Base):
    """地形数据模型"""
    __tablename__ = "terrain_data"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 采样信息
    sample_name = Column(String(100), comment="采样名称")
    sample_type = Column(String(50), default="line", comment="采样类型: line, area, point")
    
    # 地理信息
    start_longitude = Column(Float, comment="起始经度")
    start_latitude = Column(Float, comment="起始纬度")
    end_longitude = Column(Float, comment="结束经度")
    end_latitude = Column(Float, comment="结束纬度")
    
    # 采样数据
    sample_points = Column(JSON, comment="采样点数据")  # 存储采样点的详细数据
    min_height = Column(Float, comment="最小高度")
    max_height = Column(Float, comment="最大高度")
    avg_height = Column(Float, comment="平均高度")
    total_distance = Column(Float, comment="总距离")
    
    # 统计信息
    point_count = Column(Integer, comment="采样点数量")
    slope_data = Column(JSON, comment="坡度数据")
    elevation_profile = Column(JSON, comment="高程剖面数据")
    
    # 元数据
    resolution = Column(Float, comment="采样分辨率")
    accuracy = Column(Float, comment="精度")
    data_source = Column(String(100), comment="数据来源")
    
    # 系统字段
    created_by = Column(String(100), comment="创建者")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<TerrainData(id={self.id}, sample_name='{self.sample_name}', point_count={self.point_count})>"
