<template>
  <div class="monitoring-management">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="设备">
          <el-select v-model="searchForm.equipment_id" placeholder="请选择设备" clearable>
            <el-option
              v-for="equipment in equipmentStore.equipmentList"
              :key="equipment.id"
              :label="equipment.name"
              :value="equipment.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="searchForm.start_time"
            type="datetime"
            placeholder="请选择开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="searchForm.end_time"
            type="datetime"
            placeholder="请选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="数据质量">
          <el-select v-model="searchForm.data_quality" placeholder="请选择" clearable>
            <el-option label="良好" value="good" />
            <el-option label="警告" value="warning" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新增数据
        </el-button>
        <el-button
          type="success"
          :icon="Download"
          @click="handleExport"
        >
          导出数据
        </el-button>
        <el-button
          type="info"
          :icon="Refresh"
          @click="handleRefreshLatest"
        >
          刷新最新数据
        </el-button>
      </div>
    </el-card>
    
    <!-- 实时数据卡片 -->
    <el-card class="realtime-card" v-if="latestData.length > 0">
      <template #header>
        <div class="card-header">
          <span>最新监测数据</span>
          <el-tag :type="wsConnected ? 'success' : 'danger'" size="small">
            {{ wsConnected ? '实时连接' : '连接断开' }}
          </el-tag>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col
          v-for="data in latestData.slice(0, 4)"
          :key="data.id"
          :xs="24" :sm="12" :md="6"
        >
          <div class="realtime-item">
            <div class="item-header">
              <span class="equipment-name">{{ getEquipmentName(data.equipment_id) }}</span>
              <el-tag
                :type="data.data_quality === 'good' ? 'success' : 'warning'"
                size="small"
              >
                {{ data.data_quality === 'good' ? '良好' : '警告' }}
              </el-tag>
            </div>
            <div class="item-content">
              <div class="data-item">
                <span class="label">土壤温度:</span>
                <span class="value">{{ data.soil_temperature?.toFixed(1) || '-' }}°C</span>
              </div>
              <div class="data-item">
                <span class="label">土壤湿度:</span>
                <span class="value">{{ data.soil_moisture?.toFixed(1) || '-' }}%</span>
              </div>
              <div class="data-item">
                <span class="label">环境温度:</span>
                <span class="value">{{ data.ambient_temperature?.toFixed(1) || '-' }}°C</span>
              </div>
              <div class="data-item">
                <span class="label">更新时间:</span>
                <span class="value time">{{ formatTime(data.measured_at) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 数据列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="isLoading"
        :data="monitoringData"
        style="width: 100%"
        max-height="600"
      >
        <el-table-column prop="equipment_name" label="设备名称" width="120" fixed="left" />
        <el-table-column prop="soil_temperature" label="土壤温度(℃)" width="100">
          <template #default="{ row }">
            <span v-if="row.soil_temperature">{{ row.soil_temperature.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="soil_moisture" label="土壤湿度(%)" width="100">
          <template #default="{ row }">
            <span v-if="row.soil_moisture">{{ row.soil_moisture.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="soil_ph" label="土壤pH" width="80">
          <template #default="{ row }">
            <span v-if="row.soil_ph">{{ row.soil_ph.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="ambient_temperature" label="环境温度(℃)" width="100">
          <template #default="{ row }">
            <span v-if="row.ambient_temperature">{{ row.ambient_temperature.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="ambient_humidity" label="环境湿度(%)" width="100">
          <template #default="{ row }">
            <span v-if="row.ambient_humidity">{{ row.ambient_humidity.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="light_intensity" label="光照强度(lux)" width="120">
          <template #default="{ row }">
            <span v-if="row.light_intensity">{{ row.light_intensity.toFixed(0) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="monitor_battery" label="设备电量(%)" width="100">
          <template #default="{ row }">
            <el-progress
              v-if="row.monitor_battery"
              :percentage="row.monitor_battery"
              :color="getBatteryColor(row.monitor_battery)"
              :stroke-width="6"
              :show-text="false"
            />
            <span class="battery-text">{{ (row.monitor_battery || 0).toFixed(0) }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="data_quality" label="数据质量" width="80">
          <template #default="{ row }">
            <el-tag
              :type="row.data_quality === 'good' ? 'success' : 'warning'"
              size="small"
            >
              {{ row.data_quality === 'good' ? '良好' : '警告' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="measured_at" label="测量时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.measured_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 数据详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
    >
      <monitoring-detail
        v-if="currentData"
        :data="currentData"
        :equipment-name="getEquipmentName(currentData.equipment_id)"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Download, View, Delete
} from '@element-plus/icons-vue'
import { useEquipmentStore } from '@/stores/equipment'
import { monitoringApi } from '@/api/monitoring'
import { monitoringWebSocket } from '@/services/websocket'
import MonitoringDetail from './components/monitoring-detail.vue'
import type { MonitoringData } from '@/api/types'

const equipmentStore = useEquipmentStore()

// 响应式数据
const searchForm = reactive({
  equipment_id: undefined as number | undefined,
  start_time: '',
  end_time: '',
  data_quality: ''
})

const monitoringData = ref<MonitoringData[]>([])
const latestData = ref<MonitoringData[]>([])
const isLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('数据详情')
const currentData = ref<MonitoringData | null>(null)

// WebSocket 连接状态
const wsConnected = computed(() => monitoringWebSocket.isConnected.value)

// 获取设备名称
const getEquipmentName = (equipmentId: number): string => {
  const equipment = equipmentStore.findEquipmentById(equipmentId)
  return equipment?.name || `设备${equipmentId}`
}

// 获取电池颜色
const getBatteryColor = (level: number) => {
  if (level > 50) return '#67c23a'
  if (level > 20) return '#e6a23c'
  return '#f56c6c'
}

// 格式化时间
const formatTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleTimeString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMonitoringData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    equipment_id: undefined,
    start_time: '',
    end_time: '',
    data_quality: ''
  })
  handleSearch()
}

// 获取监测数据列表
const fetchMonitoringData = async () => {
  isLoading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      equipment_id: searchForm.equipment_id,
      start_time: searchForm.start_time || undefined,
      end_time: searchForm.end_time || undefined,
      data_quality: searchForm.data_quality || undefined
    }
    
    const response = await monitoringApi.getMonitoringDataList(params)
    monitoringData.value = response.items.map(item => ({
      ...item,
      equipment_name: getEquipmentName(item.equipment_id)
    }))
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch monitoring data:', error)
    ElMessage.error('获取监测数据失败')
  } finally {
    isLoading.value = false
  }
}

// 获取最新数据
const fetchLatestData = async () => {
  try {
    const data = await monitoringApi.getLatestMonitoringData()
    latestData.value = data
  } catch (error) {
    console.error('Failed to fetch latest data:', error)
  }
}

// 新增数据
const handleAdd = () => {
  ElMessage.info('新增数据功能开发中')
}

// 导出数据
const handleExport = async () => {
  try {
    // 这里应该调用导出API
    ElMessage.success('数据导出功能开发中')
  } catch (error) {
    console.error('Failed to export data:', error)
  }
}

// 刷新最新数据
const handleRefreshLatest = () => {
  fetchLatestData()
}

// 查看数据
const handleView = (data: MonitoringData) => {
  currentData.value = data
  dialogVisible.value = true
}

// 删除数据
const handleDelete = async (data: MonitoringData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条监测数据吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await monitoringApi.deleteMonitoringData(data.id)
    ElMessage.success('删除成功')
    await fetchMonitoringData()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchMonitoringData()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchMonitoringData()
}

// 处理 WebSocket 消息
const handleWebSocketMessage = (message: any) => {
  if (message.type === 'monitoring_data_update') {
    // 更新最新数据
    fetchLatestData()
    
    // 如果当前在第一页，刷新列表
    if (currentPage.value === 1) {
      fetchMonitoringData()
    }
  }
}

// 组件挂载
onMounted(async () => {
  // 获取设备列表
  await equipmentStore.fetchEquipmentList({ size: 100 })
  
  // 获取监测数据
  await Promise.all([
    fetchMonitoringData(),
    fetchLatestData()
  ])
  
  // 监听 WebSocket 消息
  monitoringWebSocket.options.onMessage = handleWebSocketMessage
})

// 组件卸载
onUnmounted(() => {
  // 清理 WebSocket 监听
  if (monitoringWebSocket.options.onMessage === handleWebSocketMessage) {
    monitoringWebSocket.options.onMessage = undefined
  }
})
</script>

<style scoped>
.monitoring-management {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.realtime-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.realtime-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  background: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.equipment-name {
  font-weight: 600;
  color: #303133;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.label {
  color: #606266;
}

.value {
  color: #303133;
  font-weight: 500;
}

.value.time {
  font-size: 12px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.no-data {
  color: #c0c4cc;
}

.battery-text {
  font-size: 12px;
  color: #606266;
  margin-left: 5px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 15px;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 5px;
}

:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
}
</style>
