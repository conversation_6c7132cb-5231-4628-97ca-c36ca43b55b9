#!/usr/bin/env python3
"""
创建默认用户脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal, engine, Base
from app.models.user import User
from app.core.security import get_password_hash

def create_default_users():
    """创建默认用户"""
    print("正在创建默认用户...")

    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 创建数据库会话
    db = SessionLocal()

    try:
        # 检查是否已有用户
        existing_user = db.query(User).first()
        if existing_user:
            print("用户已存在:")
            users = db.query(User).all()
            for user in users:
                print(f"- {user.username} ({user.email}) - {'管理员' if user.is_superuser else '普通用户'}")
            return

        # 创建管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="系统管理员",
            hashed_password=get_password_hash("admin123"),
            is_active=True,
            is_superuser=True,
            is_verified=True
        )
        db.add(admin_user)

        # 创建普通用户
        normal_user = User(
            username="user",
            email="<EMAIL>",
            full_name="普通用户",
            hashed_password=get_password_hash("user123"),
            is_active=True,
            is_superuser=False,
            is_verified=True
        )
        db.add(normal_user)

        db.commit()
        print("✅ 创建了默认用户:")
        print("- 管理员: admin / admin123")
        print("- 普通用户: user / user123")

    except Exception as e:
        print(f"❌ 创建用户时出错: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_default_users()
