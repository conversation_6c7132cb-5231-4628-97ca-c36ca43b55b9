import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'
import type { User, LoginRequest, RegisterRequest } from '@/api/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('access_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_superuser || false)
  const userPermissions = computed(() => {
    if (!user.value) return []
    
    const permissions = ['read']
    if (user.value.is_superuser) {
      permissions.push('write', 'delete', 'admin')
    }
    return permissions
  })

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        await getCurrentUser()
      } catch (error) {
        console.error('Failed to get current user:', error)
        logout()
      }
    }
  }

  // 用户登录
  const login = async (credentials: LoginRequest) => {
    isLoading.value = true
    try {
      const response = await authApi.login(credentials)
      
      // 保存 token 和用户信息
      token.value = response.access_token
      user.value = response.user
      
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('user_info', JSON.stringify(response.user))
      
      ElMessage.success('登录成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户注册
  const register = async (userData: RegisterRequest) => {
    isLoading.value = true
    try {
      const newUser = await authApi.register(userData)
      ElMessage.success('注册成功，请登录')
      return newUser
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const currentUser = await authApi.getCurrentUser()
      user.value = currentUser
      localStorage.setItem('user_info', JSON.stringify(currentUser))
      return currentUser
    } catch (error) {
      console.error('Failed to get current user:', error)
      throw error
    }
  }

  // 用户登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      
      ElMessage.success('已退出登录')
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    isLoading.value = true
    try {
      await authApi.changePassword(oldPassword, newPassword)
      ElMessage.success('密码修改成功')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '密码修改失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (email: string) => {
    isLoading.value = true
    try {
      await authApi.resetPassword(email)
      ElMessage.success('重置密码邮件已发送')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '重置密码失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return userPermissions.value.includes(permission)
  }

  // 检查是否可以访问路由
  const canAccessRoute = (routeMeta?: any): boolean => {
    if (!routeMeta?.requiresAuth) return true
    if (!isAuthenticated.value) return false
    
    if (routeMeta.requiresAdmin && !isAdmin.value) return false
    if (routeMeta.requiredPermissions) {
      return routeMeta.requiredPermissions.every((permission: string) => 
        hasPermission(permission)
      )
    }
    
    return true
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    userPermissions,
    
    // 方法
    initAuth,
    login,
    register,
    getCurrentUser,
    logout,
    changePassword,
    resetPassword,
    hasPermission,
    canAccessRoute,
  }
})
