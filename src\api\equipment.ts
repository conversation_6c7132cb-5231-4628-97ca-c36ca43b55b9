import apiClient from './index'
import type { 
  Equipment, 
  EquipmentCreate, 
  EquipmentUpdate, 
  EquipmentStatus,
  PaginatedResponse 
} from './types'

export const equipmentApi = {
  // 获取设备列表
  getEquipmentList: async (params?: {
    page?: number
    size?: number
    status?: string
    is_active?: boolean
    search?: string
  }): Promise<PaginatedResponse<Equipment>> => {
    const response = await apiClient.get('/api/v1/equipment/', { params })
    return response.data
  },

  // 获取设备状态统计
  getEquipmentStatus: async (): Promise<EquipmentStatus> => {
    const response = await apiClient.get('/api/v1/equipment/status')
    return response.data
  },

  // 获取单个设备信息
  getEquipment: async (id: number): Promise<Equipment> => {
    const response = await apiClient.get(`/api/v1/equipment/${id}`)
    return response.data
  },

  // 创建设备
  createEquipment: async (equipment: EquipmentCreate): Promise<Equipment> => {
    const response = await apiClient.post('/api/v1/equipment/', equipment)
    return response.data
  },

  // 更新设备
  updateEquipment: async (id: number, equipment: EquipmentUpdate): Promise<Equipment> => {
    const response = await apiClient.put(`/api/v1/equipment/${id}`, equipment)
    return response.data
  },

  // 删除设备
  deleteEquipment: async (id: number): Promise<void> => {
    await apiClient.delete(`/api/v1/equipment/${id}`)
  },

  // 批量删除设备
  batchDeleteEquipment: async (ids: number[]): Promise<void> => {
    await apiClient.post('/api/v1/equipment/batch-delete', { ids })
  },

  // 导出设备数据
  exportEquipment: async (params?: {
    status?: string
    is_active?: boolean
    search?: string
  }): Promise<Blob> => {
    const response = await apiClient.get('/api/v1/equipment/export', {
      params,
      responseType: 'blob',
    })
    return response.data
  },
}
