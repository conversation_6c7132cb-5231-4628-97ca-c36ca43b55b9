# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/nongye_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=nongye_db
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=农业监测系统API
VERSION=1.0.0
DESCRIPTION=农业监测系统后端API服务

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"]

# Environment
ENVIRONMENT=development
DEBUG=True

# Monitoring Data Simulation
ENABLE_DATA_SIMULATION=True
SIMULATION_INTERVAL=30  # seconds

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads/
