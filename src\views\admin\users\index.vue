<template>
  <div class="users-management">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input
            v-model="searchForm.email"
            placeholder="请输入邮箱"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.is_active" placeholder="请选择状态" clearable>
            <el-option label="激活" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.is_superuser" placeholder="请选择角色" clearable>
            <el-option label="管理员" :value="true" />
            <el-option label="普通用户" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新增用户
        </el-button>
        <el-button
          type="success"
          :icon="Download"
          @click="handleExport"
        >
          导出用户
        </el-button>
      </div>
    </el-card>
    
    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="isLoading"
        :data="userList"
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="full_name" label="真实姓名" min-width="120">
          <template #default="{ row }">
            {{ row.full_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="is_superuser" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_superuser ? 'danger' : 'primary'" size="small">
              {{ row.is_superuser ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleActive(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="is_verified" label="邮箱验证" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_verified ? 'success' : 'warning'" size="small">
              {{ row.is_verified ? '已验证' : '未验证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="login_count" label="登录次数" width="100" />
        <el-table-column prop="last_login" label="最后登录" width="160">
          <template #default="{ row }">
            {{ row.last_login ? formatDateTime(row.last_login) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.id !== authStore.user?.id"
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <user-form
        ref="userFormRef"
        :user="currentUser"
        :readonly="dialogMode === 'view'"
        @submit="handleSubmit"
      />
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          v-if="dialogMode !== 'view'"
          type="primary"
          :loading="isLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Download, View, Edit, Delete
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import UserForm from './components/user-form.vue'
import type { User } from '@/api/types'

const authStore = useAuthStore()

// 响应式数据
const searchForm = reactive({
  username: '',
  email: '',
  is_active: undefined as boolean | undefined,
  is_superuser: undefined as boolean | undefined
})

const userList = ref<User[]>([])
const isLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const currentUser = ref<User | null>(null)
const userFormRef = ref()

// 计算属性
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增用户',
    edit: '编辑用户',
    view: '用户详情'
  }
  return titleMap[dialogMode.value]
})

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    email: '',
    is_active: undefined,
    is_superuser: undefined
  })
  handleSearch()
}

// 获取用户列表（模拟数据）
const fetchUserList = async () => {
  isLoading.value = true
  try {
    // 这里应该调用真实的API
    // 模拟数据
    const mockUsers: User[] = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        full_name: '系统管理员',
        is_active: true,
        is_superuser: true,
        is_verified: true,
        last_login: new Date().toISOString(),
        login_count: 25,
        created_at: '2024-01-01T00:00:00',
        updated_at: '2024-01-01T00:00:00'
      },
      {
        id: 2,
        username: 'user',
        email: '<EMAIL>',
        full_name: '普通用户',
        is_active: true,
        is_superuser: false,
        is_verified: true,
        last_login: new Date(Date.now() - 86400000).toISOString(),
        login_count: 12,
        created_at: '2024-01-02T00:00:00',
        updated_at: '2024-01-02T00:00:00'
      }
    ]
    
    // 应用搜索过滤
    let filteredUsers = mockUsers
    if (searchForm.username) {
      filteredUsers = filteredUsers.filter(user => 
        user.username.includes(searchForm.username)
      )
    }
    if (searchForm.email) {
      filteredUsers = filteredUsers.filter(user => 
        user.email.includes(searchForm.email)
      )
    }
    if (searchForm.is_active !== undefined) {
      filteredUsers = filteredUsers.filter(user => 
        user.is_active === searchForm.is_active
      )
    }
    if (searchForm.is_superuser !== undefined) {
      filteredUsers = filteredUsers.filter(user => 
        user.is_superuser === searchForm.is_superuser
      )
    }
    
    // 分页
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    
    userList.value = filteredUsers.slice(start, end)
    total.value = filteredUsers.length
  } catch (error) {
    console.error('Failed to fetch user list:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    isLoading.value = false
  }
}

// 新增用户
const handleAdd = () => {
  dialogMode.value = 'add'
  currentUser.value = null
  dialogVisible.value = true
}

// 查看用户
const handleView = (user: User) => {
  dialogMode.value = 'view'
  currentUser.value = user
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (user: User) => {
  dialogMode.value = 'edit'
  currentUser.value = user
  dialogVisible.value = true
}

// 删除用户
const handleDelete = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用删除API
    ElMessage.success('删除成功')
    await fetchUserList()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 切换激活状态
const handleToggleActive = async (user: User) => {
  try {
    // 这里应该调用更新API
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    user.is_active = !user.is_active
    ElMessage.error('状态更新失败')
  }
}

// 导出用户
const handleExport = async () => {
  try {
    ElMessage.success('用户导出功能开发中')
  } catch (error) {
    console.error('Failed to export users:', error)
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUserList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUserList()
}

// 对话框关闭
const handleDialogClose = () => {
  currentUser.value = null
}

// 表单提交
const handleSubmit = (formData: any) => {
  // 表单组件会调用这个方法
}

// 确认操作
const handleConfirm = async () => {
  if (!userFormRef.value) return
  
  try {
    const formData = await userFormRef.value.validate()
    
    if (dialogMode.value === 'add') {
      // 这里应该调用创建API
      ElMessage.success('用户创建成功')
    } else if (dialogMode.value === 'edit') {
      // 这里应该调用更新API
      ElMessage.success('用户更新成功')
    }
    
    dialogVisible.value = false
    await fetchUserList()
  } catch (error) {
    console.error('Failed to save user:', error)
  }
}

// 组件挂载
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.users-management {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 15px;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 5px;
}
</style>
