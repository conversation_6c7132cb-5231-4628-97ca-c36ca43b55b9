from fastapi import APIRouter
from app.api.v1.endpoints import auth, equipment, monitoring_data, terrain_data, websocket

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(equipment.router, prefix="/equipment", tags=["equipment"])
api_router.include_router(monitoring_data.router, prefix="/monitoring-data", tags=["monitoring-data"])
api_router.include_router(terrain_data.router, prefix="/terrain-data", tags=["terrain-data"])
api_router.include_router(websocket.router, prefix="/ws", tags=["websocket"])
