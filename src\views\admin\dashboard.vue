<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon equipment">
              <el-icon size="40"><Monitor /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ equipmentStats.total }}</div>
              <div class="stats-label">设备总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon online">
              <el-icon size="40"><Connection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ equipmentStats.online }}</div>
              <div class="stats-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon size="40"><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ equipmentStats.low_battery }}</div>
              <div class="stats-label">低电量设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon data">
              <el-icon size="40"><DataAnalysis /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ todayDataCount }}</div>
              <div class="stats-label">今日数据</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :lg="12">
        <el-card title="设备状态分布">
          <template #header>
            <span>设备状态分布</span>
          </template>
          <div ref="equipmentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card title="数据趋势">
          <template #header>
            <span>数据趋势</span>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 实时数据和设备列表 -->
    <el-row :gutter="20" class="content-row">
      <el-col :xs="24" :lg="14">
        <el-card title="最新监测数据">
          <template #header>
            <div class="card-header">
              <span>最新监测数据</span>
              <el-button
                type="primary"
                size="small"
                :icon="Refresh"
                @click="refreshLatestData"
              >
                刷新
              </el-button>
            </div>
          </template>
          
          <el-table
            v-loading="dataLoading"
            :data="latestData"
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="equipment_name" label="设备名称" width="120" />
            <el-table-column prop="soil_temperature" label="土壤温度(℃)" width="100">
              <template #default="{ row }">
                <span v-if="row.soil_temperature">{{ row.soil_temperature.toFixed(1) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="soil_moisture" label="土壤湿度(%)" width="100">
              <template #default="{ row }">
                <span v-if="row.soil_moisture">{{ row.soil_moisture.toFixed(1) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="ambient_temperature" label="环境温度(℃)" width="100">
              <template #default="{ row }">
                <span v-if="row.ambient_temperature">{{ row.ambient_temperature.toFixed(1) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="ambient_humidity" label="环境湿度(%)" width="100">
              <template #default="{ row }">
                <span v-if="row.ambient_humidity">{{ row.ambient_humidity.toFixed(1) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="measured_at" label="测量时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.measured_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="data_quality" label="数据质量" width="80">
              <template #default="{ row }">
                <el-tag
                  :type="row.data_quality === 'good' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ row.data_quality === 'good' ? '良好' : '警告' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="10">
        <el-card title="设备状态">
          <template #header>
            <div class="card-header">
              <span>设备状态</span>
              <el-button
                type="primary"
                size="small"
                :icon="Refresh"
                @click="refreshEquipmentList"
              >
                刷新
              </el-button>
            </div>
          </template>
          
          <el-table
            v-loading="equipmentLoading"
            :data="equipmentList"
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="name" label="设备名称" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag
                  :type="getStatusType(row.status)"
                  size="small"
                >
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="battery_level" label="电量" width="80">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.battery_level || 0"
                  :color="getBatteryColor(row.battery_level)"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="battery-text">{{ (row.battery_level || 0).toFixed(0) }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor, Connection, Warning, DataAnalysis, Refresh
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useEquipmentStore } from '@/stores/equipment'
import { monitoringApi } from '@/api/monitoring'
import { monitoringWebSocket } from '@/services/websocket'
import type { MonitoringData, Equipment } from '@/api/types'

const equipmentStore = useEquipmentStore()

// 响应式数据
const equipmentStats = reactive({
  total: 0,
  online: 0,
  offline: 0,
  low_battery: 0
})

const todayDataCount = ref(0)
const latestData = ref<MonitoringData[]>([])
const equipmentList = ref<Equipment[]>([])
const dataLoading = ref(false)
const equipmentLoading = ref(false)

// 图表引用
const equipmentChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()
let equipmentChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null

// 获取设备状态统计
const fetchEquipmentStats = async () => {
  try {
    await equipmentStore.fetchEquipmentStatus()
    if (equipmentStore.equipmentStatus) {
      Object.assign(equipmentStats, equipmentStore.equipmentStatus)
    }
  } catch (error) {
    console.error('Failed to fetch equipment stats:', error)
  }
}

// 获取最新监测数据
const refreshLatestData = async () => {
  dataLoading.value = true
  try {
    const data = await monitoringApi.getLatestMonitoringData()
    latestData.value = data.map(item => ({
      ...item,
      equipment_name: equipmentStore.findEquipmentById(item.equipment_id)?.name || `设备${item.equipment_id}`
    }))
  } catch (error) {
    console.error('Failed to fetch latest data:', error)
    ElMessage.error('获取最新数据失败')
  } finally {
    dataLoading.value = false
  }
}

// 获取设备列表
const refreshEquipmentList = async () => {
  equipmentLoading.value = true
  try {
    await equipmentStore.fetchEquipmentList({ size: 10 })
    equipmentList.value = equipmentStore.equipmentList
  } catch (error) {
    console.error('Failed to fetch equipment list:', error)
    ElMessage.error('获取设备列表失败')
  } finally {
    equipmentLoading.value = false
  }
}

// 初始化设备状态图表
const initEquipmentChart = () => {
  if (!equipmentChartRef.value) return
  
  equipmentChart = echarts.init(equipmentChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: equipmentStats.online, name: '在线' },
          { value: equipmentStats.offline, name: '离线' },
          { value: equipmentStats.low_battery, name: '低电量' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  equipmentChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  trendChart = echarts.init(trendChartRef.value)
  
  // 模拟数据
  const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
  const temperatureData = Array.from({ length: 24 }, () => Math.random() * 10 + 20)
  const humidityData = Array.from({ length: 24 }, () => Math.random() * 30 + 50)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['温度', '湿度']
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: [
      {
        type: 'value',
        name: '温度(℃)',
        position: 'left'
      },
      {
        type: 'value',
        name: '湿度(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '温度',
        type: 'line',
        data: temperatureData,
        smooth: true,
        yAxisIndex: 0
      },
      {
        name: '湿度',
        type: 'line',
        data: humidityData,
        smooth: true,
        yAxisIndex: 1
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '运行中': 'success',
    '离线': 'danger',
    '维护中': 'warning',
    '低电量': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取电池颜色
const getBatteryColor = (level: number) => {
  if (level > 50) return '#67c23a'
  if (level > 20) return '#e6a23c'
  return '#f56c6c'
}

// 处理 WebSocket 消息
const handleWebSocketMessage = (message: any) => {
  if (message.type === 'monitoring_data_update') {
    // 更新最新数据
    refreshLatestData()
  } else if (message.type === 'equipment_status_update') {
    // 更新设备状态
    fetchEquipmentStats()
    refreshEquipmentList()
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  equipmentChart?.resize()
  trendChart?.resize()
}

// 组件挂载
onMounted(async () => {
  // 获取初始数据
  await Promise.all([
    fetchEquipmentStats(),
    refreshLatestData(),
    refreshEquipmentList()
  ])
  
  // 初始化图表
  await nextTick()
  initEquipmentChart()
  initTrendChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  
  // 监听 WebSocket 消息
  monitoringWebSocket.options.onMessage = handleWebSocketMessage
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  equipmentChart?.dispose()
  trendChart?.dispose()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.charts-row {
  margin-bottom: 20px;
}

.content-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
}

.stats-icon.equipment {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stats-icon.data {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.battery-text {
  font-size: 12px;
  color: #606266;
  margin-left: 5px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
}
</style>
