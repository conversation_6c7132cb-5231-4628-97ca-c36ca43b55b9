<template>
  <div class="equipment-management">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入设备名称或ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="运行中" value="运行中" />
            <el-option label="离线" value="离线" />
            <el-option label="维护中" value="维护中" />
            <el-option label="低电量" value="低电量" />
          </el-select>
        </el-form-item>
        <el-form-item label="激活状态">
          <el-select v-model="searchForm.is_active" placeholder="请选择" clearable>
            <el-option label="已激活" :value="true" />
            <el-option label="未激活" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新增设备
        </el-button>
        <el-button
          type="danger"
          :icon="Delete"
          :disabled="!selectedRows.length"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
        <el-button
          type="success"
          :icon="Download"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </div>
    </el-card>
    
    <!-- 设备列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="equipmentStore.isLoading"
        :data="equipmentStore.equipmentList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="设备名称" min-width="120" />
        <el-table-column prop="equipment_id" label="设备ID" min-width="120" />
        <el-table-column label="位置" min-width="180">
          <template #default="{ row }">
            {{ row.longitude.toFixed(6) }}, {{ row.latitude.toFixed(6) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="battery_level" label="电量" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.battery_level || 0"
              :color="getBatteryColor(row.battery_level)"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        <el-table-column prop="signal_strength" label="信号强度" width="100">
          <template #default="{ row }">
            {{ (row.signal_strength || 0).toFixed(0) }}%
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="激活状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleActive(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="equipmentStore.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 设备详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <equipment-form
        ref="equipmentFormRef"
        :equipment="currentEquipment"
        :readonly="dialogMode === 'view'"
        @submit="handleSubmit"
      />
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          v-if="dialogMode !== 'view'"
          type="primary"
          :loading="equipmentStore.isLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Delete, Download,
  View, Edit
} from '@element-plus/icons-vue'
import { useEquipmentStore } from '@/stores/equipment'
import EquipmentForm from './components/equipment-form.vue'
import type { Equipment } from '@/api/types'

const equipmentStore = useEquipmentStore()

// 响应式数据
const searchForm = reactive({
  search: '',
  status: '',
  is_active: undefined as boolean | undefined
})

const selectedRows = ref<Equipment[]>([])
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit' | 'view'>('add')
const currentEquipment = ref<Equipment | null>(null)
const equipmentFormRef = ref()

// 计算属性
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增设备',
    edit: '编辑设备',
    view: '设备详情'
  }
  return titleMap[dialogMode.value]
})

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchEquipmentList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    status: '',
    is_active: undefined
  })
  handleSearch()
}

// 获取设备列表
const fetchEquipmentList = async () => {
  const params = {
    page: currentPage.value,
    size: pageSize.value,
    search: searchForm.search || undefined,
    status: searchForm.status || undefined,
    is_active: searchForm.is_active
  }
  
  try {
    await equipmentStore.fetchEquipmentList(params)
  } catch (error) {
    console.error('Failed to fetch equipment list:', error)
  }
}

// 新增设备
const handleAdd = () => {
  dialogMode.value = 'add'
  currentEquipment.value = null
  dialogVisible.value = true
}

// 查看设备
const handleView = (equipment: Equipment) => {
  dialogMode.value = 'view'
  currentEquipment.value = equipment
  dialogVisible.value = true
}

// 编辑设备
const handleEdit = (equipment: Equipment) => {
  dialogMode.value = 'edit'
  currentEquipment.value = equipment
  dialogVisible.value = true
}

// 删除设备
const handleDelete = async (equipment: Equipment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备 "${equipment.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await equipmentStore.deleteEquipment(equipment.id)
    await fetchEquipmentList()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个设备吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id)
    await equipmentStore.batchDeleteEquipment(ids)
    selectedRows.value = []
    await fetchEquipmentList()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 导出数据
const handleExport = async () => {
  try {
    await equipmentStore.exportEquipment({
      search: searchForm.search || undefined,
      status: searchForm.status || undefined,
      is_active: searchForm.is_active
    })
  } catch (error) {
    console.error('Failed to export equipment:', error)
  }
}

// 切换激活状态
const handleToggleActive = async (equipment: Equipment) => {
  try {
    await equipmentStore.updateEquipment(equipment.id, {
      is_active: equipment.is_active
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    equipment.is_active = !equipment.is_active
  }
}

// 选择变化
const handleSelectionChange = (selection: Equipment[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchEquipmentList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchEquipmentList()
}

// 对话框关闭
const handleDialogClose = () => {
  currentEquipment.value = null
}

// 表单提交
const handleSubmit = (formData: any) => {
  // 表单组件会调用这个方法
}

// 确认操作
const handleConfirm = async () => {
  if (!equipmentFormRef.value) return
  
  try {
    const formData = await equipmentFormRef.value.validate()
    
    if (dialogMode.value === 'add') {
      await equipmentStore.createEquipment(formData)
    } else if (dialogMode.value === 'edit') {
      await equipmentStore.updateEquipment(currentEquipment.value!.id, formData)
    }
    
    dialogVisible.value = false
    await fetchEquipmentList()
  } catch (error) {
    console.error('Failed to save equipment:', error)
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '运行中': 'success',
    '离线': 'danger',
    '维护中': 'warning',
    '低电量': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取电池颜色
const getBatteryColor = (level: number) => {
  if (level > 50) return '#67c23a'
  if (level > 20) return '#e6a23c'
  return '#f56c6c'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
  fetchEquipmentList()
})
</script>

<style scoped>
.equipment-management {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 15px;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 5px;
}
</style>
