<template>
  <div class="terrain-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="采样名称">
        {{ terrain.sample_name || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="采样类型">
        <el-tag :type="getSampleTypeColor(terrain.sample_type)" size="small">
          {{ getSampleTypeText(terrain.sample_type) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建者">
        {{ terrain.created_by || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="数据来源">
        {{ terrain.data_source || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDateTime(terrain.created_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ formatDateTime(terrain.updated_at) }}
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="left">位置信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-statistic
          title="起始经度"
          :value="terrain.start_longitude || 0"
          :precision="6"
        />
      </el-col>
      <el-col :span="12">
        <el-statistic
          title="起始纬度"
          :value="terrain.start_latitude || 0"
          :precision="6"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-statistic
          title="结束经度"
          :value="terrain.end_longitude || 0"
          :precision="6"
        />
      </el-col>
      <el-col :span="12">
        <el-statistic
          title="结束纬度"
          :value="terrain.end_latitude || 0"
          :precision="6"
        />
      </el-col>
    </el-row>
    
    <el-divider content-position="left">统计信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-statistic
          title="采样点数量"
          :value="terrain.point_count || 0"
          suffix="个"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="总距离"
          :value="terrain.total_distance || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="采样分辨率"
          :value="terrain.resolution || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-statistic
          title="最小高程"
          :value="terrain.min_height || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="最大高程"
          :value="terrain.max_height || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="平均高程"
          :value="terrain.avg_height || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-statistic
          title="数据精度"
          :value="terrain.accuracy || 0"
          suffix="m"
          :precision="2"
        />
      </el-col>
    </el-row>
    
    <div v-if="terrain.sample_points && terrain.sample_points.length > 0" style="margin-top: 30px;">
      <el-divider content-position="left">采样点数据</el-divider>
      <el-table
        :data="samplePointsData"
        style="width: 100%"
        max-height="300"
        size="small"
      >
        <el-table-column prop="index" label="序号" width="80" />
        <el-table-column prop="longitude" label="经度" width="120">
          <template #default="{ row }">
            {{ row.longitude?.toFixed(6) || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="latitude" label="纬度" width="120">
          <template #default="{ row }">
            {{ row.latitude?.toFixed(6) || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="height" label="高程(m)" width="100">
          <template #default="{ row }">
            {{ row.height?.toFixed(1) || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="distance" label="距离(m)" width="100">
          <template #default="{ row }">
            {{ row.distance?.toFixed(1) || '-' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div v-if="terrain.slope_data && terrain.slope_data.length > 0" style="margin-top: 30px;">
      <el-divider content-position="left">坡度数据</el-divider>
      <div class="chart-container" ref="slopeChartRef"></div>
    </div>
    
    <div v-if="terrain.elevation_profile && terrain.elevation_profile.length > 0" style="margin-top: 30px;">
      <el-divider content-position="left">高程剖面</el-divider>
      <div class="chart-container" ref="elevationChartRef"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { TerrainData } from '@/api/types'

interface Props {
  terrain: TerrainData
}

const props = defineProps<Props>()

// 图表引用
const slopeChartRef = ref<HTMLElement>()
const elevationChartRef = ref<HTMLElement>()

// 采样点数据
const samplePointsData = computed(() => {
  if (!props.terrain.sample_points) return []
  return props.terrain.sample_points.slice(0, 100) // 限制显示前100个点
})

// 获取采样类型文本
const getSampleTypeText = (type?: string) => {
  const typeMap: Record<string, string> = {
    line: '线采样',
    area: '面采样',
    point: '点采样'
  }
  return typeMap[type || ''] || type || '-'
}

// 获取采样类型颜色
const getSampleTypeColor = (type?: string) => {
  const colorMap: Record<string, string> = {
    line: 'primary',
    area: 'success',
    point: 'warning'
  }
  return colorMap[type || ''] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 初始化坡度图表
const initSlopeChart = () => {
  if (!slopeChartRef.value || !props.terrain.slope_data) return
  
  const chart = echarts.init(slopeChartRef.value)
  
  const xData = props.terrain.slope_data.map((_, index) => index + 1)
  const yData = props.terrain.slope_data.map(item => item.slope || 0)
  
  const option = {
    title: {
      text: '坡度变化图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: xData,
      name: '采样点'
    },
    yAxis: {
      type: 'value',
      name: '坡度(°)'
    },
    series: [
      {
        name: '坡度',
        type: 'line',
        data: yData,
        smooth: true,
        lineStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化高程剖面图表
const initElevationChart = () => {
  if (!elevationChartRef.value || !props.terrain.elevation_profile) return
  
  const chart = echarts.init(elevationChartRef.value)
  
  const xData = props.terrain.elevation_profile.map(item => item.distance || 0)
  const yData = props.terrain.elevation_profile.map(item => item.height || 0)
  
  const option = {
    title: {
      text: '高程剖面图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const point = params[0]
        return `距离: ${point.name}m<br/>高程: ${point.value}m`
      }
    },
    xAxis: {
      type: 'category',
      data: xData,
      name: '距离(m)'
    },
    yAxis: {
      type: 'value',
      name: '高程(m)'
    },
    series: [
      {
        name: '高程',
        type: 'line',
        data: yData,
        smooth: true,
        lineStyle: {
          color: '#67c23a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initSlopeChart()
    initElevationChart()
  })
})
</script>

<style scoped>
.terrain-detail {
  padding: 10px 0;
}

.chart-container {
  height: 300px;
  width: 100%;
}

:deep(.el-statistic__content) {
  font-size: 18px;
}

:deep(.el-statistic__number) {
  color: #409eff;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #303133;
}
</style>
