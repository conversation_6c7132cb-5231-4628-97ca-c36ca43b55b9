from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

class TerrainSamplePoint(BaseModel):
    """地形采样点模型"""
    index: int = Field(..., description="采样点索引")
    longitude: float = Field(..., description="经度")
    latitude: float = Field(..., description="纬度")
    height: float = Field(..., description="高度")
    distance: Optional[float] = Field(None, description="距离起点的距离")

class TerrainDataBase(BaseModel):
    """地形数据基础模型"""
    sample_name: Optional[str] = Field(None, description="采样名称")
    sample_type: Optional[str] = Field("line", description="采样类型")
    start_longitude: Optional[float] = Field(None, description="起始经度")
    start_latitude: Optional[float] = Field(None, description="起始纬度")
    end_longitude: Optional[float] = Field(None, description="结束经度")
    end_latitude: Optional[float] = Field(None, description="结束纬度")
    sample_points: Optional[List[Dict[str, Any]]] = Field(None, description="采样点数据")
    min_height: Optional[float] = Field(None, description="最小高度")
    max_height: Optional[float] = Field(None, description="最大高度")
    avg_height: Optional[float] = Field(None, description="平均高度")
    total_distance: Optional[float] = Field(None, description="总距离")
    point_count: Optional[int] = Field(None, description="采样点数量")
    slope_data: Optional[List[Dict[str, Any]]] = Field(None, description="坡度数据")
    elevation_profile: Optional[List[Dict[str, Any]]] = Field(None, description="高程剖面数据")
    resolution: Optional[float] = Field(None, description="采样分辨率")
    accuracy: Optional[float] = Field(None, description="精度")
    data_source: Optional[str] = Field(None, description="数据来源")
    created_by: Optional[str] = Field(None, description="创建者")

class TerrainDataCreate(TerrainDataBase):
    """创建地形数据模型"""
    pass

class TerrainDataUpdate(BaseModel):
    """更新地形数据模型"""
    sample_name: Optional[str] = None
    sample_type: Optional[str] = None
    start_longitude: Optional[float] = None
    start_latitude: Optional[float] = None
    end_longitude: Optional[float] = None
    end_latitude: Optional[float] = None
    sample_points: Optional[List[Dict[str, Any]]] = None
    min_height: Optional[float] = None
    max_height: Optional[float] = None
    avg_height: Optional[float] = None
    total_distance: Optional[float] = None
    point_count: Optional[int] = None
    slope_data: Optional[List[Dict[str, Any]]] = None
    elevation_profile: Optional[List[Dict[str, Any]]] = None
    resolution: Optional[float] = None
    accuracy: Optional[float] = None
    data_source: Optional[str] = None
    created_by: Optional[str] = None

class TerrainData(TerrainDataBase):
    """地形数据响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class TerrainDataList(BaseModel):
    """地形数据列表响应模型"""
    items: List[TerrainData]
    total: int
    page: int
    size: int
    pages: int

class TerrainAnalysis(BaseModel):
    """地形分析结果模型"""
    terrain_id: int
    elevation_stats: Dict[str, float]
    slope_stats: Dict[str, float]
    terrain_roughness: float
    terrain_classification: str
