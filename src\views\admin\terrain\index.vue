<template>
  <div class="terrain-management">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="采样名称">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入采样名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="采样类型">
          <el-select v-model="searchForm.sample_type" placeholder="请选择类型" clearable>
            <el-option label="线采样" value="line" />
            <el-option label="面采样" value="area" />
            <el-option label="点采样" value="point" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建者">
          <el-input
            v-model="searchForm.created_by"
            placeholder="请输入创建者"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          新增地形数据
        </el-button>
        <el-button
          type="success"
          :icon="Download"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </div>
    </el-card>
    
    <!-- 地形数据列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="isLoading"
        :data="terrainData"
        style="width: 100%"
      >
        <el-table-column prop="sample_name" label="采样名称" min-width="150" />
        <el-table-column prop="sample_type" label="采样类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getSampleTypeColor(row.sample_type)" size="small">
              {{ getSampleTypeText(row.sample_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="起始位置" min-width="180">
          <template #default="{ row }">
            <span v-if="row.start_longitude && row.start_latitude">
              {{ row.start_longitude.toFixed(6) }}, {{ row.start_latitude.toFixed(6) }}
            </span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column label="结束位置" min-width="180">
          <template #default="{ row }">
            <span v-if="row.end_longitude && row.end_latitude">
              {{ row.end_longitude.toFixed(6) }}, {{ row.end_latitude.toFixed(6) }}
            </span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="point_count" label="采样点数" width="100">
          <template #default="{ row }">
            {{ row.point_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="高程范围" width="150">
          <template #default="{ row }">
            <span v-if="row.min_height !== null && row.max_height !== null">
              {{ row.min_height.toFixed(1) }} ~ {{ row.max_height.toFixed(1) }}m
            </span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="avg_height" label="平均高程(m)" width="120">
          <template #default="{ row }">
            <span v-if="row.avg_height">{{ row.avg_height.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_distance" label="总距离(m)" width="120">
          <template #default="{ row }">
            <span v-if="row.total_distance">{{ row.total_distance.toFixed(1) }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_by" label="创建者" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="info"
              size="small"
              :icon="DataAnalysis"
              @click="handleAnalysis(row)"
            >
              分析
            </el-button>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 地形数据详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="地形数据详情"
      width="900px"
    >
      <terrain-detail
        v-if="currentTerrain"
        :terrain="currentTerrain"
      />
    </el-dialog>
    
    <!-- 地形分析对话框 -->
    <el-dialog
      v-model="analysisDialogVisible"
      title="地形分析结果"
      width="800px"
    >
      <terrain-analysis
        v-if="currentAnalysis"
        :analysis="currentAnalysis"
        :terrain="currentTerrain"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Plus, Download, View, Delete, DataAnalysis
} from '@element-plus/icons-vue'
import { terrainApi } from '@/api/terrain'
import TerrainDetail from './components/terrain-detail.vue'
import TerrainAnalysis from './components/terrain-analysis.vue'
import type { TerrainData, TerrainAnalysis as TerrainAnalysisType } from '@/api/types'

// 响应式数据
const searchForm = reactive({
  search: '',
  sample_type: '',
  created_by: ''
})

const terrainData = ref<TerrainData[]>([])
const isLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const detailDialogVisible = ref(false)
const analysisDialogVisible = ref(false)
const currentTerrain = ref<TerrainData | null>(null)
const currentAnalysis = ref<TerrainAnalysisType | null>(null)

// 获取采样类型文本
const getSampleTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    line: '线采样',
    area: '面采样',
    point: '点采样'
  }
  return typeMap[type] || type
}

// 获取采样类型颜色
const getSampleTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    line: 'primary',
    area: 'success',
    point: 'warning'
  }
  return colorMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTerrainData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    sample_type: '',
    created_by: ''
  })
  handleSearch()
}

// 获取地形数据列表
const fetchTerrainData = async () => {
  isLoading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      search: searchForm.search || undefined,
      sample_type: searchForm.sample_type || undefined,
      created_by: searchForm.created_by || undefined
    }
    
    const response = await terrainApi.getTerrainDataList(params)
    terrainData.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch terrain data:', error)
    ElMessage.error('获取地形数据失败')
  } finally {
    isLoading.value = false
  }
}

// 新增地形数据
const handleAdd = () => {
  ElMessage.info('新增地形数据功能开发中')
}

// 导出数据
const handleExport = async () => {
  try {
    ElMessage.success('数据导出功能开发中')
  } catch (error) {
    console.error('Failed to export terrain data:', error)
  }
}

// 查看地形数据
const handleView = (terrain: TerrainData) => {
  currentTerrain.value = terrain
  detailDialogVisible.value = true
}

// 地形分析
const handleAnalysis = async (terrain: TerrainData) => {
  try {
    const analysis = await terrainApi.getTerrainAnalysis(terrain.id)
    currentTerrain.value = terrain
    currentAnalysis.value = analysis
    analysisDialogVisible.value = true
  } catch (error) {
    console.error('Failed to get terrain analysis:', error)
    ElMessage.error('获取地形分析失败')
  }
}

// 删除地形数据
const handleDelete = async (terrain: TerrainData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除地形数据 "${terrain.sample_name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await terrainApi.deleteTerrainData(terrain.id)
    ElMessage.success('删除成功')
    await fetchTerrainData()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchTerrainData()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchTerrainData()
}

// 组件挂载
onMounted(() => {
  fetchTerrainData()
})
</script>

<style scoped>
.terrain-management {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.no-data {
  color: #c0c4cc;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 15px;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 5px;
}
</style>
