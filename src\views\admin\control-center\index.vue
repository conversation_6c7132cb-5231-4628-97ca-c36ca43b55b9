<template>
  <div class="control-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">🌾 智能环境控制中心</h1>
          <p class="page-subtitle">实时监测 · 智能决策 · 精准控制 · 最佳范围维持</p>
        </div>
        <div class="system-status">
          <el-tag 
            :type="overallStatus.type" 
            size="large" 
            class="status-tag"
          >
            <el-icon><CircleCheck v-if="overallStatus.type === 'success'" /><Warning v-else /></el-icon>
            {{ overallStatus.text }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 核心数据仪表盘 -->
    <div class="dashboard-section">
      <h2 class="section-title">📊 核心数据仪表盘</h2>
      
      <!-- 环境数据面板 -->
      <div class="data-panels">
        <div class="panel-group">
          <h3 class="panel-title">🌡️ 环境参数监控</h3>
          <div class="parameter-grid">
            <div 
              v-for="param in environmentParams" 
              :key="param.key"
              class="parameter-card"
              :class="param.status"
            >
              <div class="param-header">
                <span class="param-icon">{{ param.icon }}</span>
                <span class="param-name">{{ param.name }}</span>
                <el-tag 
                  :type="param.status === 'optimal' ? 'success' : param.status === 'warning' ? 'warning' : 'danger'"
                  size="small"
                >
                  {{ param.statusText }}
                </el-tag>
              </div>
              
              <div class="param-value">
                <span class="current-value">{{ param.currentValue }}</span>
                <span class="unit">{{ param.unit }}</span>
              </div>
              
              <!-- 最佳范围指示器 -->
              <div class="range-indicator">
                <div class="range-bar">
                  <div class="range-track">
                    <div 
                      class="optimal-zone" 
                      :style="{ 
                        left: param.optimalRange.start + '%', 
                        width: (param.optimalRange.end - param.optimalRange.start) + '%' 
                      }"
                    ></div>
                    <div 
                      class="current-pointer" 
                      :style="{ left: param.currentPosition + '%' }"
                    ></div>
                  </div>
                </div>
                <div class="range-labels">
                  <span class="min-label">{{ param.minValue }}</span>
                  <span class="optimal-label">最佳范围: {{ param.optimalMin }} - {{ param.optimalMax }}</span>
                  <span class="max-label">{{ param.maxValue }}</span>
                </div>
              </div>
              
              <!-- 趋势图 -->
              <div class="trend-chart">
                <canvas 
                  :ref="el => trendChartRefs[param.key] = el"
                  :id="`trend-${param.key}`"
                  width="200" 
                  height="60"
                ></canvas>
              </div>
              
              <!-- 控制状态 -->
              <div v-if="param.controlStatus" class="control-status">
                <el-icon class="control-icon"><Operation /></el-icon>
                <span class="control-text">{{ param.controlStatus }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 生理数据面板 -->
        <div class="panel-group">
          <h3 class="panel-title">🌱 生理状态推算</h3>
          <div class="physiology-grid">
            <div 
              v-for="physio in physiologyParams" 
              :key="physio.key"
              class="physiology-card"
              :class="physio.status"
            >
              <div class="physio-header">
                <span class="physio-icon">{{ physio.icon }}</span>
                <span class="physio-name">{{ physio.name }}</span>
                <el-progress 
                  :percentage="physio.efficiency" 
                  :color="physio.color"
                  :stroke-width="8"
                  class="efficiency-bar"
                />
              </div>
              <div class="physio-value">
                <span class="value">{{ physio.value }}</span>
                <span class="unit">{{ physio.unit }}</span>
              </div>
              <div class="physio-description">{{ physio.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能控制逻辑展示 -->
    <div class="control-logic-section">
      <h2 class="section-title">⚙️ 智能控制逻辑</h2>
      <div class="logic-flow">
        <div class="flow-step" v-for="(step, index) in controlFlow" :key="index">
          <div class="step-icon">{{ step.icon }}</div>
          <div class="step-content">
            <h4 class="step-title">{{ step.title }}</h4>
            <p class="step-description">{{ step.description }}</p>
          </div>
          <div v-if="index < controlFlow.length - 1" class="flow-arrow">→</div>
        </div>
      </div>
    </div>

    <!-- 实时控制操作 -->
    <div class="active-controls-section">
      <h2 class="section-title">🎯 当前控制操作</h2>
      <div class="active-controls">
        <div 
          v-for="control in activeControls" 
          :key="control.id"
          class="control-item"
          :class="control.type"
        >
          <div class="control-header">
            <span class="control-icon">{{ control.icon }}</span>
            <span class="control-name">{{ control.name }}</span>
            <el-tag :type="control.status === 'active' ? 'success' : 'info'" size="small">
              {{ control.statusText }}
            </el-tag>
          </div>
          <div class="control-details">
            <p class="control-reason">{{ control.reason }}</p>
            <div class="control-progress">
              <el-progress 
                :percentage="control.progress" 
                :color="control.progressColor"
                :stroke-width="6"
              />
              <span class="progress-text">{{ control.progressText }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史数据与成果展示 -->
    <div class="results-section">
      <h2 class="section-title">📈 控制成果与价值证明</h2>
      <div class="results-grid">
        <!-- 稳定率指标 -->
        <div class="result-card">
          <div class="result-header">
            <span class="result-icon">🎯</span>
            <span class="result-title">环境稳定率</span>
          </div>
          <div class="result-value">
            <span class="big-number">{{ stabilityRate }}%</span>
            <span class="target">(目标 >90%)</span>
          </div>
          <div class="result-description">参数稳定在最佳范围的时间占比</div>
        </div>

        <!-- 干预效果 -->
        <div class="result-card">
          <div class="result-header">
            <span class="result-icon">⚡</span>
            <span class="result-title">异常恢复时间</span>
          </div>
          <div class="result-value">
            <span class="big-number">{{ recoveryTime }}</span>
            <span class="unit">分钟</span>
          </div>
          <div class="result-description">平均异常参数恢复到最佳范围的时间</div>
        </div>

        <!-- 产量提升 -->
        <div class="result-card">
          <div class="result-header">
            <span class="result-icon">📈</span>
            <span class="result-title">预期增产</span>
          </div>
          <div class="result-value">
            <span class="big-number">+{{ yieldIncrease }}%</span>
          </div>
          <div class="result-description">相比传统管理方式的产量提升</div>
        </div>

        <!-- 资源节省 -->
        <div class="result-card">
          <div class="result-header">
            <span class="result-icon">💧</span>
            <span class="result-title">节水效果</span>
          </div>
          <div class="result-value">
            <span class="big-number">-{{ waterSaving }}%</span>
          </div>
          <div class="result-description">精准灌溉带来的用水量减少</div>
        </div>
      </div>
    </div>

    <!-- 警报与通知中心 -->
    <div class="alerts-section">
      <h2 class="section-title">🔔 警报与通知中心</h2>
      <div class="alerts-container">
        <div class="current-alerts">
          <h3>当前活动警报</h3>
          <div v-if="currentAlerts.length === 0" class="no-alerts">
            <el-icon class="success-icon"><CircleCheck /></el-icon>
            <span>所有参数正常，无活动警报</span>
          </div>
          <div v-else class="alert-list">
            <div 
              v-for="alert in currentAlerts" 
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <el-icon class="alert-icon"><Warning /></el-icon>
              <div class="alert-content">
                <span class="alert-message">{{ alert.message }}</span>
                <span class="alert-time">{{ alert.time }}</span>
              </div>
              <div class="alert-action">{{ alert.action }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { CircleCheck, Warning, Operation } from '@element-plus/icons-vue'

// 响应式数据
const trendChartRefs = ref<Record<string, HTMLCanvasElement>>({})

// 整体系统状态
const overallStatus = reactive({
  type: 'success' as 'success' | 'warning' | 'danger',
  text: '所有参数均在最佳范围'
})

// 环境参数数据
const environmentParams = reactive([
  {
    key: 'temperature',
    name: '水温',
    icon: '🌡️',
    currentValue: 26.5,
    unit: '°C',
    status: 'optimal',
    statusText: '最佳',
    minValue: 15,
    maxValue: 35,
    optimalMin: 24,
    optimalMax: 28,
    optimalRange: { start: 30, end: 70 }, // 百分比位置
    currentPosition: 50, // 当前值在范围中的百分比位置
    controlStatus: '自动调温中'
  },
  {
    key: 'ph',
    name: 'pH值',
    icon: '⚗️',
    currentValue: 6.8,
    unit: '',
    status: 'optimal',
    statusText: '最佳',
    minValue: 5.0,
    maxValue: 9.0,
    optimalMin: 6.5,
    optimalMax: 7.5,
    optimalRange: { start: 37.5, end: 62.5 },
    currentPosition: 45,
    controlStatus: null
  },
  {
    key: 'oxygen',
    name: '溶解氧',
    icon: '💨',
    currentValue: 8.2,
    unit: 'mg/L',
    status: 'optimal',
    statusText: '最佳',
    minValue: 0,
    maxValue: 15,
    optimalMin: 6,
    optimalMax: 10,
    optimalRange: { start: 40, end: 66.7 },
    currentPosition: 54.7,
    controlStatus: '增氧机运行中'
  },
  {
    key: 'light',
    name: '光照强度',
    icon: '☀️',
    currentValue: 45000,
    unit: 'lux',
    status: 'warning',
    statusText: '偏低',
    minValue: 0,
    maxValue: 80000,
    optimalMin: 40000,
    optimalMax: 60000,
    optimalRange: { start: 50, end: 75 },
    currentPosition: 56.25,
    controlStatus: '补光灯启动中'
  }
])

// 生理参数数据
const physiologyParams = reactive([
  {
    key: 'photosynthesis',
    name: '光合作用效率',
    icon: '🌱',
    value: 85,
    unit: '%',
    efficiency: 85,
    color: '#67c23a',
    status: 'optimal',
    description: '基于光照、温度、CO2浓度综合计算'
  },
  {
    key: 'transpiration',
    name: '蒸腾速率',
    icon: '💧',
    value: 2.3,
    unit: 'mm/h',
    efficiency: 78,
    color: '#409eff',
    status: 'optimal',
    description: '根据温湿度、风速等环境因子推算'
  },
  {
    key: 'stress',
    name: '胁迫指数',
    icon: '⚠️',
    value: 0.15,
    unit: '',
    efficiency: 15,
    color: '#f56c6c',
    status: 'optimal',
    description: '综合环境压力评估，数值越低越好'
  }
])

// 控制流程
const controlFlow = [
  {
    icon: '📡',
    title: '实时监测',
    description: '传感器网络24/7采集环境数据'
  },
  {
    icon: '🧠',
    title: 'AI分析',
    description: '机器学习模型预测趋势和生理状态'
  },
  {
    icon: '⚖️',
    title: '范围比对',
    description: '对比当前值与最佳范围阈值'
  },
  {
    icon: '🎯',
    title: '智能决策',
    description: '基于规则引擎和预测模型制定控制策略'
  },
  {
    icon: '⚡',
    title: '精准执行',
    description: '自动控制设备精确调节环境参数'
  },
  {
    icon: '✅',
    title: '效果验证',
    description: '监测控制效果，确保参数回归最佳范围'
  }
]

// 当前控制操作
const activeControls = reactive([
  {
    id: 1,
    name: '智能灌溉系统',
    icon: '💧',
    type: 'irrigation',
    status: 'active',
    statusText: '运行中',
    reason: '土壤湿度低于最佳范围下限，启动精准灌溉',
    progress: 65,
    progressColor: '#409eff',
    progressText: '预计5分钟后完成'
  },
  {
    id: 2,
    name: '补光系统',
    icon: '💡',
    type: 'lighting',
    status: 'active',
    statusText: '运行中',
    reason: '光照强度不足，自动启动LED补光',
    progress: 30,
    progressColor: '#e6a23c',
    progressText: '补光中，预计持续2小时'
  }
])

// 成果数据
const stabilityRate = ref(94.2)
const recoveryTime = ref(3.5)
const yieldIncrease = ref(15.8)
const waterSaving = ref(22.3)

// 当前警报
const currentAlerts = reactive([])

// 绘制趋势图
const drawTrendChart = (canvas: HTMLCanvasElement, data: number[]) => {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const width = canvas.width
  const height = canvas.height
  
  ctx.clearRect(0, 0, width, height)
  
  // 绘制背景最佳范围
  ctx.fillStyle = 'rgba(103, 194, 58, 0.1)'
  ctx.fillRect(0, height * 0.3, width, height * 0.4)
  
  // 绘制数据线
  ctx.strokeStyle = '#409eff'
  ctx.lineWidth = 2
  ctx.beginPath()
  
  data.forEach((value, index) => {
    const x = (index / (data.length - 1)) * width
    const y = height - (value * height)
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.stroke()
}

// 模拟数据更新
const updateData = () => {
  // 这里可以添加实际的数据更新逻辑
  // 例如从WebSocket或API获取最新数据
}

onMounted(() => {
  // 绘制趋势图
  setTimeout(() => {
    Object.keys(trendChartRefs.value).forEach(key => {
      const canvas = trendChartRefs.value[key]
      if (canvas) {
        // 模拟24小时趋势数据
        const trendData = Array.from({ length: 24 }, () => Math.random() * 0.4 + 0.3)
        drawTrendChart(canvas, trendData)
      }
    })
  }, 100)

  // 设置定时更新
  const interval = setInterval(updateData, 30000) // 30秒更新一次
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.control-center {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面标题 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}

.status-tag {
  font-size: 16px;
  padding: 12px 20px;
  border-radius: 8px;
}

/* 章节标题 */
.section-title {
  font-size: 22px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
  padding-left: 12px;
  border-left: 4px solid #27ae60;
}

/* 仪表盘部分 */
.dashboard-section {
  margin-bottom: 32px;
}

.data-panels {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.panel-group {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  color: #34495e;
  margin: 0 0 20px 0;
}

/* 环境参数网格 */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.parameter-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.parameter-card.optimal {
  border-color: #27ae60;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.parameter-card.warning {
  border-color: #f39c12;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.parameter-card.danger {
  border-color: #e74c3c;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.param-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.param-icon {
  font-size: 24px;
  margin-right: 8px;
}

.param-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.param-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 16px;
}

.current-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.unit {
  font-size: 16px;
  color: #7f8c8d;
  margin-left: 4px;
}

/* 范围指示器 */
.range-indicator {
  margin-bottom: 16px;
}

.range-bar {
  height: 8px;
  margin-bottom: 8px;
}

.range-track {
  position: relative;
  height: 100%;
  background: #ecf0f1;
  border-radius: 4px;
}

.optimal-zone {
  position: absolute;
  height: 100%;
  background: #27ae60;
  border-radius: 4px;
  opacity: 0.7;
}

.current-pointer {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  background: #3498db;
  border-radius: 50%;
  border: 2px solid white;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #7f8c8d;
}

.optimal-label {
  font-weight: 600;
  color: #27ae60;
}

/* 趋势图 */
.trend-chart {
  margin-bottom: 12px;
}

.trend-chart canvas {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.5);
}

/* 控制状态 */
.control-status {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 6px;
  border-left: 3px solid #3498db;
}

.control-icon {
  color: #3498db;
  margin-right: 8px;
}

.control-text {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

/* 生理参数网格 */
.physiology-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.physiology-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #ecf0f1;
}

.physio-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.physio-icon {
  font-size: 20px;
  margin-right: 8px;
}

.physio-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.efficiency-bar {
  width: 100px;
}

.physio-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.physio-value .value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.physio-description {
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
}

/* 控制逻辑流程 */
.control-logic-section {
  margin-bottom: 32px;
}

.logic-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 150px;
}

.step-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.step-description {
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
  margin: 0;
}

.flow-arrow {
  font-size: 24px;
  color: #3498db;
  margin: 0 10px;
}

/* 活动控制 */
.active-controls-section {
  margin-bottom: 32px;
}

.active-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.control-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.control-icon {
  font-size: 20px;
  margin-right: 8px;
}

.control-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.control-details {
  margin-top: 12px;
}

.control-reason {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.control-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 12px;
  color: #7f8c8d;
  white-space: nowrap;
}

/* 成果展示 */
.results-section {
  margin-bottom: 32px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.result-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.result-icon {
  font-size: 24px;
  margin-right: 8px;
}

.result-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.result-value {
  margin-bottom: 12px;
}

.big-number {
  font-size: 36px;
  font-weight: 700;
  color: #27ae60;
}

.target {
  font-size: 14px;
  color: #7f8c8d;
  margin-left: 8px;
}

.result-description {
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
}

/* 警报中心 */
.alerts-section {
  margin-bottom: 32px;
}

.alerts-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.current-alerts h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
}

.no-alerts {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #27ae60;
  font-size: 16px;
}

.success-icon {
  font-size: 24px;
  margin-right: 8px;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
  background: #fff3cd;
}

.alert-icon {
  color: #f39c12;
  margin-right: 12px;
}

.alert-content {
  flex: 1;
}

.alert-message {
  display: block;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.alert-time {
  display: block;
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
}

.alert-action {
  font-size: 12px;
  color: #3498db;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-center {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .parameter-grid,
  .physiology-grid {
    grid-template-columns: 1fr;
  }

  .logic-flow {
    flex-direction: column;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .results-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .results-grid {
    grid-template-columns: 1fr;
  }

  .active-controls {
    grid-template-columns: 1fr;
  }
}
</style>
