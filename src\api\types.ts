// API 响应基础类型
export interface ApiResponse<T = any> {
  code?: number
  message?: string
  data?: T
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user: User
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name?: string
}

// 设备相关类型
export interface Equipment {
  id: number
  name: string
  equipment_id: string
  longitude: number
  latitude: number
  altitude?: number
  status: string
  battery_level?: number
  signal_strength?: number
  model?: string
  manufacturer?: string
  firmware_version?: string
  installation_date?: string
  last_maintenance?: string
  next_maintenance?: string
  maintenance_notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface EquipmentCreate {
  name: string
  equipment_id: string
  longitude: number
  latitude: number
  altitude?: number
  status?: string
  battery_level?: number
  signal_strength?: number
  model?: string
  manufacturer?: string
  firmware_version?: string
  installation_date?: string
  last_maintenance?: string
  next_maintenance?: string
  maintenance_notes?: string
  is_active?: boolean
}

export interface EquipmentUpdate {
  name?: string
  longitude?: number
  latitude?: number
  altitude?: number
  status?: string
  battery_level?: number
  signal_strength?: number
  model?: string
  manufacturer?: string
  firmware_version?: string
  installation_date?: string
  last_maintenance?: string
  next_maintenance?: string
  maintenance_notes?: string
  is_active?: boolean
}

export interface EquipmentStatus {
  total: number
  active: number
  inactive: number
  online: number
  offline: number
  low_battery: number
  maintenance_due: number
}

// 监测数据相关类型
export interface MonitoringData {
  id: number
  equipment_id: number
  soil_temperature?: number
  soil_moisture?: number
  soil_ph?: number
  soil_conductivity?: number
  soil_nitrogen?: number
  soil_phosphorus?: number
  soil_potassium?: number
  ambient_temperature?: number
  ambient_humidity?: number
  light_intensity?: number
  uv_index?: number
  wind_speed?: number
  wind_direction?: number
  atmospheric_pressure?: number
  rainfall?: number
  monitor_battery?: number
  signal_quality?: number
  data_quality?: string
  error_code?: string
  error_message?: string
  longitude?: number
  latitude?: number
  altitude?: number
  measured_at: string
  created_at: string
  updated_at: string
}

export interface MonitoringDataCreate {
  equipment_id: number
  soil_temperature?: number
  soil_moisture?: number
  soil_ph?: number
  soil_conductivity?: number
  soil_nitrogen?: number
  soil_phosphorus?: number
  soil_potassium?: number
  ambient_temperature?: number
  ambient_humidity?: number
  light_intensity?: number
  uv_index?: number
  wind_speed?: number
  wind_direction?: number
  atmospheric_pressure?: number
  rainfall?: number
  monitor_battery?: number
  signal_quality?: number
  data_quality?: string
  error_code?: string
  error_message?: string
  longitude?: number
  latitude?: number
  altitude?: number
  measured_at: string
}

export interface MonitoringDataStats {
  equipment_id: number
  latest_data?: MonitoringData
  avg_soil_temperature?: number
  avg_soil_moisture?: number
  avg_ambient_temperature?: number
  avg_ambient_humidity?: number
  avg_light_intensity?: number
  data_count: number
  last_update?: string
}

// 地形数据相关类型
export interface TerrainData {
  id: number
  sample_name?: string
  sample_type?: string
  start_longitude?: number
  start_latitude?: number
  end_longitude?: number
  end_latitude?: number
  sample_points?: any[]
  min_height?: number
  max_height?: number
  avg_height?: number
  total_distance?: number
  point_count?: number
  slope_data?: any[]
  elevation_profile?: any[]
  resolution?: number
  accuracy?: number
  data_source?: string
  created_by?: string
  created_at: string
  updated_at: string
}

export interface TerrainDataCreate {
  sample_name?: string
  sample_type?: string
  start_longitude?: number
  start_latitude?: number
  end_longitude?: number
  end_latitude?: number
  sample_points?: any[]
  min_height?: number
  max_height?: number
  avg_height?: number
  total_distance?: number
  point_count?: number
  slope_data?: any[]
  elevation_profile?: any[]
  resolution?: number
  accuracy?: number
  data_source?: string
  created_by?: string
}

export interface TerrainAnalysis {
  terrain_id: number
  elevation_stats: Record<string, number>
  slope_stats: Record<string, number>
  terrain_roughness: number
  terrain_classification: string
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string
  data?: any
  timestamp?: string
  equipment_id?: number
  message?: string
}
