<template>
  <div class="welcome-container">
    <div class="welcome-header">
      <h1>🌾 农业监测系统</h1>
      <p>基于 Vue3 + Cesium + FastAPI 的智能农业监测管理平台</p>
    </div>
    
    <div class="feature-cards">
      <div class="feature-card" @click="goToLogin">
        <div class="card-icon">
          <el-icon size="48"><User /></el-icon>
        </div>
        <h3>用户登录</h3>
        <p>登录系统访问管理功能</p>
        <div class="card-footer">
          <el-button type="primary" size="large">立即登录</el-button>
        </div>
      </div>
      
      <div class="feature-card" @click="goToRegister">
        <div class="card-icon">
          <el-icon size="48"><UserFilled /></el-icon>
        </div>
        <h3>用户注册</h3>
        <p>创建新账号开始使用</p>
        <div class="card-footer">
          <el-button type="success" size="large">注册账号</el-button>
        </div>
      </div>
      
      <div class="feature-card" @click="goToDemo">
        <div class="card-icon">
          <el-icon size="48"><Monitor /></el-icon>
        </div>
        <h3>演示页面</h3>
        <p>查看系统演示和功能展示</p>
        <div class="card-footer">
          <el-button type="info" size="large">查看演示</el-button>
        </div>
      </div>
    </div>
    
    <div class="system-info">
      <el-card>
        <template #header>
          <span>系统信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端服务">
            <el-tag type="success">运行中</el-tag>
            <span style="margin-left: 10px;">http://localhost:5173</span>
          </el-descriptions-item>
          <el-descriptions-item label="后端服务">
            <el-tag :type="backendStatus ? 'success' : 'danger'">
              {{ backendStatus ? '运行中' : '离线' }}
            </el-tag>
            <span style="margin-left: 10px;">http://localhost:8000</span>
          </el-descriptions-item>
          <el-descriptions-item label="API文档">
            <el-link type="primary" @click="openApiDocs">
              http://localhost:8000/docs
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="WebSocket">
            <el-tag :type="wsStatus ? 'success' : 'warning'">
              {{ wsStatus ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
    
    <div class="default-accounts">
      <el-card>
        <template #header>
          <span>默认测试账号</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="account-info">
              <h4>管理员账号</h4>
              <p><strong>用户名：</strong>admin</p>
              <p><strong>密码：</strong>admin123</p>
              <p><strong>权限：</strong>完整管理权限</p>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="account-info">
              <h4>普通用户账号</h4>
              <p><strong>用户名：</strong>user</p>
              <p><strong>密码：</strong>user123</p>
              <p><strong>权限：</strong>基础查看权限</p>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <div class="quick-actions">
      <el-button-group>
        <el-button type="primary" @click="quickLogin('admin')">
          <el-icon><User /></el-icon>
          管理员快速登录
        </el-button>
        <el-button type="success" @click="quickLogin('user')">
          <el-icon><UserFilled /></el-icon>
          普通用户快速登录
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, UserFilled, Monitor } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import apiClient from '@/api'

const router = useRouter()
const authStore = useAuthStore()

const backendStatus = ref(false)
const wsStatus = ref(false)

// 检查后端服务状态
const checkBackendStatus = async () => {
  try {
    await apiClient.get('/api/v1/equipment/')
    backendStatus.value = true
  } catch (error) {
    backendStatus.value = false
  }
}

// 检查 WebSocket 状态
const checkWebSocketStatus = () => {
  try {
    const ws = new WebSocket('ws://localhost:8000/api/v1/ws/monitoring')
    ws.onopen = () => {
      wsStatus.value = true
      ws.close()
    }
    ws.onerror = () => {
      wsStatus.value = false
    }
  } catch (error) {
    wsStatus.value = false
  }
}

// 导航方法
const goToLogin = () => {
  router.push('/login')
}

const goToRegister = () => {
  router.push('/register')
}

const goToDemo = () => {
  router.push('/carbon-neutral-bigscreen')
}

const openApiDocs = () => {
  window.open('http://localhost:8000/docs', '_blank')
}

// 快速登录
const quickLogin = async (userType: 'admin' | 'user') => {
  const credentials = {
    admin: { username: 'admin', password: 'admin123' },
    user: { username: 'user', password: 'user123' }
  }
  
  try {
    await authStore.login(credentials[userType])
    router.push('/admin')
  } catch (error) {
    ElMessage.error(`${userType === 'admin' ? '管理员' : '普通用户'}登录失败`)
  }
}

// 组件挂载时检查服务状态
onMounted(() => {
  checkBackendStatus()
  checkWebSocketStatus()
})
</script>

<style scoped>
.welcome-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.welcome-header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.welcome-header h1 {
  font-size: 3rem;
  margin: 0 0 16px 0;
  font-weight: 700;
}

.welcome-header p {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto 40px auto;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.card-icon {
  color: #409eff;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin: 0 0 12px 0;
  color: #333;
}

.feature-card p {
  color: #666;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.card-footer {
  margin-top: 20px;
}

.system-info,
.default-accounts {
  max-width: 1200px;
  margin: 0 auto 30px auto;
}

.account-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.account-info h4 {
  margin: 0 0 12px 0;
  color: #333;
}

.account-info p {
  margin: 8px 0;
  color: #666;
}

.quick-actions {
  text-align: center;
  margin-top: 30px;
}

:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-weight: 600;
}

:deep(.el-button-group .el-button) {
  padding: 12px 24px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .welcome-header h1 {
    font-size: 2rem;
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-card {
    padding: 20px;
  }
}
</style>
