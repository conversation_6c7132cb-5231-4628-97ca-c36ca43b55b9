import apiClient from './index'
import type { LoginRequest, LoginResponse, RegisterRequest, User } from './types'

export const authApi = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)
    
    const response = await apiClient.post('/api/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  // 用户注册
  register: async (userData: RegisterRequest): Promise<User> => {
    const response = await apiClient.post('/api/v1/auth/register', userData)
    return response.data
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get('/api/v1/auth/me')
    return response.data
  },

  // 刷新 token
  refreshToken: async (): Promise<LoginResponse> => {
    const response = await apiClient.post('/api/v1/auth/refresh')
    return response.data
  },

  // 用户登出
  logout: async (): Promise<void> => {
    await apiClient.post('/api/v1/auth/logout')
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<void> => {
    await apiClient.post('/api/v1/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword,
    })
  },

  // 重置密码
  resetPassword: async (email: string): Promise<void> => {
    await apiClient.post('/api/v1/auth/reset-password', { email })
  },
}
