# 农业监测系统测试指南

## 系统概述

这是一个基于 Vue3 + Cesium + FastAPI 的农业监测管理系统，包含以下主要功能：

### 前端功能
1. **3D地图展示** - 基于Cesium的三维地图
2. **设备管理** - 监测设备的增删改查
3. **监测数据管理** - 实时数据展示和历史数据查询
4. **地形数据管理** - 地形采样和分析
5. **用户管理** - 用户权限管理
6. **系统设置** - 系统配置管理
7. **用户认证** - 登录/注册/权限控制

### 后端功能
1. **RESTful API** - 完整的API接口
2. **WebSocket** - 实时数据推送
3. **用户认证** - JWT令牌认证
4. **数据模拟** - 自动生成模拟数据
5. **数据库管理** - SQLite数据库

## 测试步骤

### 1. 启动后端服务

```bash
cd backend
python main.py
```

后端服务将在 http://localhost:8000 启动

### 2. 启动前端服务

```bash
npm run dev
```

前端服务将在 http://localhost:5173 启动

### 3. 初始化数据

```bash
cd backend
python scripts/init_data.py
```

这将创建：
- 默认用户账号
- 示例设备数据
- 模拟监测数据

### 4. 默认账号

系统提供以下默认账号：

**管理员账号：**
- 用户名: admin
- 密码: admin123

**普通用户账号：**
- 用户名: user  
- 密码: user123

### 5. 功能测试

#### 5.1 用户认证测试
1. 访问 http://localhost:5173
2. 点击登录，使用默认账号登录
3. 验证登录成功后跳转到管理后台

#### 5.2 设备管理测试
1. 进入"设备管理"页面
2. 查看设备列表
3. 测试新增、编辑、删除设备
4. 验证设备状态切换

#### 5.3 监测数据测试
1. 进入"监测数据"页面
2. 查看实时数据卡片
3. 查看历史数据列表
4. 测试数据筛选和分页

#### 5.4 地形数据测试
1. 进入"地形数据"页面
2. 查看地形数据列表
3. 测试地形分析功能

#### 5.5 用户管理测试（管理员）
1. 使用管理员账号登录
2. 进入"用户管理"页面
3. 测试用户的增删改查
4. 测试用户状态切换

#### 5.6 系统设置测试（管理员）
1. 进入"系统设置"页面
2. 查看系统配置选项
3. 测试配置保存功能

### 6. API测试

访问 http://localhost:8000/docs 查看API文档并测试接口

### 7. WebSocket测试

1. 打开浏览器开发者工具
2. 查看Network标签页的WebSocket连接
3. 验证实时数据推送

## 已知问题

1. **Cesium地图** - 需要配置Cesium Ion访问令牌
2. **邮件功能** - 邮件发送功能需要配置SMTP服务器
3. **文件上传** - 文件上传功能需要配置存储路径

## 技术栈

### 前端
- Vue 3.4+
- TypeScript
- Element Plus
- Cesium
- Pinia
- Vue Router
- Axios
- ECharts

### 后端
- FastAPI
- SQLAlchemy
- SQLite
- WebSocket
- JWT认证
- Pydantic

## 项目结构

```
nongye123/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # 公共组件
│   │   ├── stores/          # Pinia状态管理
│   │   ├── views/           # 页面组件
│   │   ├── router/          # 路由配置
│   │   └── services/        # 服务层
│   └── package.json
├── backend/                 # 后端项目
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模型
│   │   └── services/       # 业务逻辑
│   ├── scripts/            # 脚本文件
│   └── requirements.txt
└── README.md
```

## 开发建议

1. **代码规范** - 使用ESLint和Prettier保持代码风格一致
2. **类型安全** - 充分利用TypeScript的类型检查
3. **组件复用** - 提取公共组件提高代码复用性
4. **错误处理** - 完善错误处理和用户提示
5. **性能优化** - 使用懒加载和虚拟滚动优化性能
6. **测试覆盖** - 编写单元测试和集成测试
7. **文档完善** - 保持API文档和代码注释的更新

## 部署说明

### 开发环境
- 前端: `npm run dev`
- 后端: `python main.py`

### 生产环境
- 前端: `npm run build` 然后部署到Web服务器
- 后端: 使用Gunicorn或uWSGI部署FastAPI应用

## 联系方式

如有问题请联系开发团队或查看项目文档。
