<template>
  <div class="terrain-analysis">
    <el-alert
      :title="`地形分类: ${analysis.terrain_classification}`"
      :type="getClassificationType(analysis.terrain_classification)"
      show-icon
      :closable="false"
      style="margin-bottom: 20px;"
    />
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>高程统计</span>
          </template>
          <el-row :gutter="15">
            <el-col :span="12">
              <el-statistic
                title="最小值"
                :value="analysis.elevation_stats.min"
                suffix="m"
                :precision="1"
              />
            </el-col>
            <el-col :span="12">
              <el-statistic
                title="最大值"
                :value="analysis.elevation_stats.max"
                suffix="m"
                :precision="1"
              />
            </el-col>
          </el-row>
          <el-row :gutter="15" style="margin-top: 15px;">
            <el-col :span="12">
              <el-statistic
                title="平均值"
                :value="analysis.elevation_stats.mean"
                suffix="m"
                :precision="1"
              />
            </el-col>
            <el-col :span="12">
              <el-statistic
                title="标准差"
                :value="analysis.elevation_stats.std"
                suffix="m"
                :precision="2"
              />
            </el-col>
          </el-row>
          <el-row style="margin-top: 15px;">
            <el-col :span="24">
              <el-statistic
                title="高程范围"
                :value="analysis.elevation_stats.range"
                suffix="m"
                :precision="1"
              />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>坡度统计</span>
          </template>
          <el-row :gutter="15">
            <el-col :span="12">
              <el-statistic
                title="最小坡度"
                :value="analysis.slope_stats.min"
                suffix="°"
                :precision="1"
              />
            </el-col>
            <el-col :span="12">
              <el-statistic
                title="最大坡度"
                :value="analysis.slope_stats.max"
                suffix="°"
                :precision="1"
              />
            </el-col>
          </el-row>
          <el-row :gutter="15" style="margin-top: 15px;">
            <el-col :span="12">
              <el-statistic
                title="平均坡度"
                :value="analysis.slope_stats.mean"
                suffix="°"
                :precision="1"
              />
            </el-col>
            <el-col :span="12">
              <el-statistic
                title="标准差"
                :value="analysis.slope_stats.std"
                suffix="°"
                :precision="2"
              />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>地形特征</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic
                title="地形粗糙度"
                :value="analysis.terrain_roughness"
                :precision="2"
              />
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-label">地形分类</div>
                <el-tag
                  :type="getClassificationType(analysis.terrain_classification)"
                  size="large"
                >
                  {{ analysis.terrain_classification }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="feature-item">
                <div class="feature-label">适宜性评价</div>
                <el-rate
                  :model-value="getSuitabilityRating(analysis.terrain_classification)"
                  disabled
                  show-score
                  text-color="#ff9900"
                />
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>分析图表</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container" ref="elevationChartRef"></div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container" ref="slopeChartRef"></div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>分析建议</span>
          </template>
          <div class="suggestions">
            <el-alert
              v-for="suggestion in getSuggestions(analysis)"
              :key="suggestion.title"
              :title="suggestion.title"
              :description="suggestion.description"
              :type="suggestion.type"
              show-icon
              :closable="false"
              style="margin-bottom: 10px;"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { TerrainAnalysis, TerrainData } from '@/api/types'

interface Props {
  analysis: TerrainAnalysis
  terrain?: TerrainData
}

const props = defineProps<Props>()

// 图表引用
const elevationChartRef = ref<HTMLElement>()
const slopeChartRef = ref<HTMLElement>()

// 获取地形分类类型
const getClassificationType = (classification: string) => {
  const typeMap: Record<string, string> = {
    '平坦': 'success',
    '缓坡': 'info',
    '中等起伏': 'warning',
    '陡峭': 'danger'
  }
  return typeMap[classification] || 'info'
}

// 获取适宜性评级
const getSuitabilityRating = (classification: string) => {
  const ratingMap: Record<string, number> = {
    '平坦': 5,
    '缓坡': 4,
    '中等起伏': 3,
    '陡峭': 2
  }
  return ratingMap[classification] || 3
}

// 获取分析建议
const getSuggestions = (analysis: TerrainAnalysis) => {
  const suggestions = []
  
  // 基于地形分类的建议
  switch (analysis.terrain_classification) {
    case '平坦':
      suggestions.push({
        title: '地形优势',
        description: '地形平坦，适合大规模机械化作业，便于灌溉系统建设。',
        type: 'success'
      })
      break
    case '缓坡':
      suggestions.push({
        title: '适度坡度',
        description: '坡度适中，有利于自然排水，适合多种作物种植。',
        type: 'info'
      })
      break
    case '中等起伏':
      suggestions.push({
        title: '注意水土保持',
        description: '地形起伏较大，需要采取水土保持措施，建议等高线种植。',
        type: 'warning'
      })
      break
    case '陡峭':
      suggestions.push({
        title: '坡度过大',
        description: '地形陡峭，不适合机械化作业，建议改为林地或采取梯田措施。',
        type: 'error'
      })
      break
  }
  
  // 基于高程范围的建议
  if (analysis.elevation_stats.range > 50) {
    suggestions.push({
      title: '高程差异大',
      description: '区域内高程差异较大，需要考虑不同海拔的气候差异对作物的影响。',
      type: 'warning'
    })
  }
  
  // 基于地形粗糙度的建议
  if (analysis.terrain_roughness > 20) {
    suggestions.push({
      title: '地形复杂',
      description: '地形较为复杂，建议进行详细的小区域规划，因地制宜选择作物。',
      type: 'info'
    })
  }
  
  return suggestions
}

// 初始化高程分布图表
const initElevationChart = () => {
  if (!elevationChartRef.value) return
  
  const chart = echarts.init(elevationChartRef.value)
  
  // 模拟高程分布数据
  const elevationData = []
  const min = props.analysis.elevation_stats.min
  const max = props.analysis.elevation_stats.max
  const step = (max - min) / 10
  
  for (let i = 0; i < 10; i++) {
    const range = `${(min + i * step).toFixed(1)}-${(min + (i + 1) * step).toFixed(1)}`
    const count = Math.floor(Math.random() * 20) + 5
    elevationData.push({ name: range, value: count })
  }
  
  const option = {
    title: {
      text: '高程分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    xAxis: {
      type: 'category',
      data: elevationData.map(item => item.name),
      axisLabel: { rotate: 45, fontSize: 10 }
    },
    yAxis: {
      type: 'value',
      name: '点数'
    },
    series: [
      {
        name: '高程分布',
        type: 'bar',
        data: elevationData.map(item => item.value),
        itemStyle: {
          color: '#409eff'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化坡度分布图表
const initSlopeChart = () => {
  if (!slopeChartRef.value) return
  
  const chart = echarts.init(slopeChartRef.value)
  
  // 模拟坡度分布数据
  const slopeRanges = ['0-5°', '5-10°', '10-15°', '15-20°', '20-25°', '>25°']
  const slopeData = slopeRanges.map(range => ({
    name: range,
    value: Math.floor(Math.random() * 30) + 10
  }))
  
  const option = {
    title: {
      text: '坡度分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '坡度分布',
        type: 'pie',
        radius: '60%',
        data: slopeData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initElevationChart()
    initSlopeChart()
  })
})
</script>

<style scoped>
.terrain-analysis {
  padding: 10px 0;
}

.chart-container {
  height: 250px;
  width: 100%;
}

.feature-item {
  text-align: center;
}

.feature-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.suggestions {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.el-statistic__content) {
  font-size: 16px;
}

:deep(.el-statistic__number) {
  color: #409eff;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  font-weight: 600;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
