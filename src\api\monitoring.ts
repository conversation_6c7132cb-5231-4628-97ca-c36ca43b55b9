import apiClient from './index'
import type { 
  MonitoringData, 
  MonitoringDataCreate, 
  MonitoringDataStats,
  PaginatedResponse 
} from './types'

export const monitoringApi = {
  // 获取监测数据列表
  getMonitoringDataList: async (params?: {
    page?: number
    size?: number
    equipment_id?: number
    start_time?: string
    end_time?: string
    data_quality?: string
  }): Promise<PaginatedResponse<MonitoringData>> => {
    const response = await apiClient.get('/api/v1/monitoring-data/', { params })
    return response.data
  },

  // 获取最新监测数据
  getLatestMonitoringData: async (equipment_ids?: string): Promise<MonitoringData[]> => {
    const params = equipment_ids ? { equipment_ids } : undefined
    const response = await apiClient.get('/api/v1/monitoring-data/latest', { params })
    return response.data
  },

  // 获取设备监测数据统计
  getMonitoringDataStats: async (equipment_id: number, days?: number): Promise<MonitoringDataStats> => {
    const params = days ? { days } : undefined
    const response = await apiClient.get(`/api/v1/monitoring-data/stats/${equipment_id}`, { params })
    return response.data
  },

  // 获取单条监测数据
  getMonitoringData: async (id: number): Promise<MonitoringData> => {
    const response = await apiClient.get(`/api/v1/monitoring-data/${id}`)
    return response.data
  },

  // 创建监测数据
  createMonitoringData: async (data: MonitoringDataCreate): Promise<MonitoringData> => {
    const response = await apiClient.post('/api/v1/monitoring-data/', data)
    return response.data
  },

  // 批量创建监测数据
  batchCreateMonitoringData: async (dataList: MonitoringDataCreate[]): Promise<MonitoringData[]> => {
    const response = await apiClient.post('/api/v1/monitoring-data/batch', dataList)
    return response.data
  },

  // 更新监测数据
  updateMonitoringData: async (id: number, data: Partial<MonitoringDataCreate>): Promise<MonitoringData> => {
    const response = await apiClient.put(`/api/v1/monitoring-data/${id}`, data)
    return response.data
  },

  // 删除监测数据
  deleteMonitoringData: async (id: number): Promise<void> => {
    await apiClient.delete(`/api/v1/monitoring-data/${id}`)
  },

  // 导出监测数据
  exportMonitoringData: async (params?: {
    equipment_id?: number
    start_time?: string
    end_time?: string
    data_quality?: string
  }): Promise<Blob> => {
    const response = await apiClient.get('/api/v1/monitoring-data/export', {
      params,
      responseType: 'blob',
    })
    return response.data
  },

  // 获取数据趋势图表数据
  getDataTrends: async (params: {
    equipment_id: number
    start_time: string
    end_time: string
    metrics: string[]
  }): Promise<any> => {
    const response = await apiClient.get('/api/v1/monitoring-data/trends', { params })
    return response.data
  },
}
