from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
import numpy as np

from app.core.database import get_db
from app.models.terrain_data import TerrainData as TerrainDataModel
from app.schemas.terrain_data import (
    TerrainData, TerrainDataCreate, TerrainDataUpdate,
    TerrainDataList, TerrainAnalysis
)

router = APIRouter()

@router.get("/", response_model=TerrainDataList)
def get_terrain_data_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    sample_type: Optional[str] = Query(None, description="采样类型筛选"),
    created_by: Optional[str] = Query(None, description="创建者筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db)
):
    """获取地形数据列表"""
    query = db.query(TerrainDataModel)
    
    # 应用筛选条件
    if sample_type:
        query = query.filter(TerrainDataModel.sample_type == sample_type)
    if created_by:
        query = query.filter(TerrainDataModel.created_by == created_by)
    if search:
        query = query.filter(TerrainDataModel.sample_name.contains(search))
    
    # 按创建时间倒序排列
    query = query.order_by(desc(TerrainDataModel.created_at))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.offset(offset).limit(size).all()
    
    # 计算总页数
    pages = (total + size - 1) // size
    
    return TerrainDataList(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/{terrain_id}", response_model=TerrainData)
def get_terrain_data(terrain_id: int, db: Session = Depends(get_db)):
    """获取单个地形数据"""
    terrain = db.query(TerrainDataModel).filter(TerrainDataModel.id == terrain_id).first()
    if not terrain:
        raise HTTPException(status_code=404, detail="地形数据不存在")
    return terrain

@router.get("/{terrain_id}/analysis", response_model=TerrainAnalysis)
def get_terrain_analysis(terrain_id: int, db: Session = Depends(get_db)):
    """获取地形分析结果"""
    terrain = db.query(TerrainDataModel).filter(TerrainDataModel.id == terrain_id).first()
    if not terrain:
        raise HTTPException(status_code=404, detail="地形数据不存在")
    
    if not terrain.sample_points:
        raise HTTPException(status_code=400, detail="没有采样点数据")
    
    # 提取高程数据
    heights = [point.get('height', 0) for point in terrain.sample_points if 'height' in point]
    
    if not heights:
        raise HTTPException(status_code=400, detail="没有有效的高程数据")
    
    heights_array = np.array(heights)
    
    # 计算高程统计
    elevation_stats = {
        "min": float(np.min(heights_array)),
        "max": float(np.max(heights_array)),
        "mean": float(np.mean(heights_array)),
        "std": float(np.std(heights_array)),
        "range": float(np.max(heights_array) - np.min(heights_array))
    }
    
    # 计算坡度（简化计算）
    if len(heights) > 1:
        slopes = []
        for i in range(1, len(heights)):
            # 假设相邻点距离相等
            slope = abs(heights[i] - heights[i-1])
            slopes.append(slope)
        
        slopes_array = np.array(slopes)
        slope_stats = {
            "min": float(np.min(slopes_array)),
            "max": float(np.max(slopes_array)),
            "mean": float(np.mean(slopes_array)),
            "std": float(np.std(slopes_array))
        }
    else:
        slope_stats = {"min": 0, "max": 0, "mean": 0, "std": 0}
    
    # 计算地形粗糙度
    terrain_roughness = float(np.std(heights_array))
    
    # 地形分类（简化）
    if terrain_roughness < 5:
        terrain_classification = "平坦"
    elif terrain_roughness < 15:
        terrain_classification = "缓坡"
    elif terrain_roughness < 30:
        terrain_classification = "中等起伏"
    else:
        terrain_classification = "陡峭"
    
    return TerrainAnalysis(
        terrain_id=terrain_id,
        elevation_stats=elevation_stats,
        slope_stats=slope_stats,
        terrain_roughness=terrain_roughness,
        terrain_classification=terrain_classification
    )

@router.post("/", response_model=TerrainData)
def create_terrain_data(terrain: TerrainDataCreate, db: Session = Depends(get_db)):
    """创建地形数据"""
    # 如果有采样点数据，计算统计信息
    terrain_dict = terrain.dict()
    
    if terrain_dict.get('sample_points'):
        sample_points = terrain_dict['sample_points']
        heights = [point.get('height', 0) for point in sample_points if isinstance(point, dict) and 'height' in point]
        
        if heights:
            terrain_dict['min_height'] = min(heights)
            terrain_dict['max_height'] = max(heights)
            terrain_dict['avg_height'] = sum(heights) / len(heights)
            terrain_dict['point_count'] = len(sample_points)
            
            # 计算总距离（如果有位置信息）
            if len(sample_points) > 1:
                total_distance = 0
                for i in range(1, len(sample_points)):
                    if all(key in sample_points[i-1] and key in sample_points[i] 
                          for key in ['longitude', 'latitude']):
                        # 简化距离计算
                        dx = sample_points[i]['longitude'] - sample_points[i-1]['longitude']
                        dy = sample_points[i]['latitude'] - sample_points[i-1]['latitude']
                        distance = (dx**2 + dy**2)**0.5 * 111000  # 粗略转换为米
                        total_distance += distance
                terrain_dict['total_distance'] = total_distance
    
    db_terrain = TerrainDataModel(**terrain_dict)
    db.add(db_terrain)
    db.commit()
    db.refresh(db_terrain)
    return db_terrain

@router.put("/{terrain_id}", response_model=TerrainData)
def update_terrain_data(
    terrain_id: int,
    terrain_update: TerrainDataUpdate,
    db: Session = Depends(get_db)
):
    """更新地形数据"""
    terrain = db.query(TerrainDataModel).filter(TerrainDataModel.id == terrain_id).first()
    if not terrain:
        raise HTTPException(status_code=404, detail="地形数据不存在")
    
    update_data = terrain_update.dict(exclude_unset=True)
    
    # 如果更新了采样点数据，重新计算统计信息
    if 'sample_points' in update_data and update_data['sample_points']:
        sample_points = update_data['sample_points']
        heights = [point.get('height', 0) for point in sample_points if isinstance(point, dict) and 'height' in point]
        
        if heights:
            update_data['min_height'] = min(heights)
            update_data['max_height'] = max(heights)
            update_data['avg_height'] = sum(heights) / len(heights)
            update_data['point_count'] = len(sample_points)
    
    for field, value in update_data.items():
        setattr(terrain, field, value)
    
    db.commit()
    db.refresh(terrain)
    return terrain

@router.delete("/{terrain_id}")
def delete_terrain_data(terrain_id: int, db: Session = Depends(get_db)):
    """删除地形数据"""
    terrain = db.query(TerrainDataModel).filter(TerrainDataModel.id == terrain_id).first()
    if not terrain:
        raise HTTPException(status_code=404, detail="地形数据不存在")
    
    db.delete(terrain)
    db.commit()
    return {"message": "地形数据删除成功"}
