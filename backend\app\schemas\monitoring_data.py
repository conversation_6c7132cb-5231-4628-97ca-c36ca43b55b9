from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

class MonitoringDataBase(BaseModel):
    """监测数据基础模型"""
    equipment_id: int = Field(..., description="设备ID")
    
    # 土壤数据
    soil_temperature: Optional[float] = Field(None, description="土壤温度(℃)")
    soil_moisture: Optional[float] = Field(None, description="土壤湿度(%)")
    soil_ph: Optional[float] = Field(None, description="土壤酸碱度")
    soil_conductivity: Optional[float] = Field(None, description="土壤电导率")
    soil_nitrogen: Optional[float] = Field(None, description="土壤氮含量")
    soil_phosphorus: Optional[float] = Field(None, description="土壤磷含量")
    soil_potassium: Optional[float] = Field(None, description="土壤钾含量")
    
    # 环境数据
    ambient_temperature: Optional[float] = Field(None, description="环境温度(℃)")
    ambient_humidity: Optional[float] = Field(None, description="环境湿度(%)")
    light_intensity: Optional[float] = Field(None, description="光照强度(lux)")
    uv_index: Optional[float] = Field(None, description="紫外线指数")
    wind_speed: Optional[float] = Field(None, description="风速(m/s)")
    wind_direction: Optional[float] = Field(None, description="风向(度)")
    atmospheric_pressure: Optional[float] = Field(None, description="大气压强(hPa)")
    rainfall: Optional[float] = Field(None, description="降雨量(mm)")
    
    # 设备状态数据
    monitor_battery: Optional[float] = Field(None, description="监测器电量(%)")
    signal_quality: Optional[float] = Field(None, description="信号质量")
    
    # 数据质量
    data_quality: Optional[str] = Field("good", description="数据质量")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    # 位置信息
    longitude: Optional[float] = Field(None, description="测量点经度")
    latitude: Optional[float] = Field(None, description="测量点纬度")
    altitude: Optional[float] = Field(None, description="测量点海拔")
    
    measured_at: datetime = Field(..., description="测量时间")

class MonitoringDataCreate(MonitoringDataBase):
    """创建监测数据模型"""
    pass

class MonitoringDataUpdate(BaseModel):
    """更新监测数据模型"""
    soil_temperature: Optional[float] = None
    soil_moisture: Optional[float] = None
    soil_ph: Optional[float] = None
    soil_conductivity: Optional[float] = None
    soil_nitrogen: Optional[float] = None
    soil_phosphorus: Optional[float] = None
    soil_potassium: Optional[float] = None
    ambient_temperature: Optional[float] = None
    ambient_humidity: Optional[float] = None
    light_intensity: Optional[float] = None
    uv_index: Optional[float] = None
    wind_speed: Optional[float] = None
    wind_direction: Optional[float] = None
    atmospheric_pressure: Optional[float] = None
    rainfall: Optional[float] = None
    monitor_battery: Optional[float] = None
    signal_quality: Optional[float] = None
    data_quality: Optional[str] = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None
    altitude: Optional[float] = None

class MonitoringData(MonitoringDataBase):
    """监测数据响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MonitoringDataList(BaseModel):
    """监测数据列表响应模型"""
    items: List[MonitoringData]
    total: int
    page: int
    size: int
    pages: int

class MonitoringDataStats(BaseModel):
    """监测数据统计模型"""
    equipment_id: int
    latest_data: Optional[MonitoringData]
    avg_soil_temperature: Optional[float]
    avg_soil_moisture: Optional[float]
    avg_ambient_temperature: Optional[float]
    avg_ambient_humidity: Optional[float]
    avg_light_intensity: Optional[float]
    data_count: int
    last_update: Optional[datetime]
