from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

class MonitoringData(Base):
    """农田监测数据模型"""
    __tablename__ = "monitoring_data"
    
    id = Column(Integer, primary_key=True, index=True)
    equipment_id = Column(Integer, ForeignKey("equipment.id"), nullable=False, comment="设备ID")
    
    # 土壤数据
    soil_temperature = Column(Float, comment="土壤温度(℃)")
    soil_moisture = Column(Float, comment="土壤湿度(%)")
    soil_ph = Column(Float, comment="土壤酸碱度")
    soil_conductivity = Column(Float, comment="土壤电导率")
    soil_nitrogen = Column(Float, comment="土壤氮含量")
    soil_phosphorus = Column(Float, comment="土壤磷含量")
    soil_potassium = Column(Float, comment="土壤钾含量")
    
    # 环境数据
    ambient_temperature = Column(Float, comment="环境温度(℃)")
    ambient_humidity = Column(Float, comment="环境湿度(%)")
    light_intensity = Column(Float, comment="光照强度(lux)")
    uv_index = Column(Float, comment="紫外线指数")
    wind_speed = Column(Float, comment="风速(m/s)")
    wind_direction = Column(Float, comment="风向(度)")
    atmospheric_pressure = Column(Float, comment="大气压强(hPa)")
    rainfall = Column(Float, comment="降雨量(mm)")
    
    # 设备状态数据
    monitor_battery = Column(Float, comment="监测器电量(%)")
    signal_quality = Column(Float, comment="信号质量")
    
    # 数据质量
    data_quality = Column(String(20), default="good", comment="数据质量")
    error_code = Column(String(50), comment="错误代码")
    error_message = Column(Text, comment="错误信息")
    
    # 位置信息（可能与设备位置不同，用于移动设备）
    longitude = Column(Float, comment="测量点经度")
    latitude = Column(Float, comment="测量点纬度")
    altitude = Column(Float, comment="测量点海拔")
    
    # 系统字段
    measured_at = Column(DateTime, nullable=False, comment="测量时间")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    equipment = relationship("Equipment", back_populates="monitoring_data")
    
    def __repr__(self):
        return f"<MonitoringData(id={self.id}, equipment_id={self.equipment_id}, measured_at='{self.measured_at}')>"
