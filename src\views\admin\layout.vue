<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarCollapsed ? '64px' : '250px'" class="sidebar">
        <div class="sidebar-header">
          <div v-if="!sidebarCollapsed" class="logo">
            <img src="@/assets/logo.png" alt="Logo" class="logo-img">
            <span class="logo-text">农业监测系统</span>
          </div>
          <div v-else class="logo-collapsed">
            <img src="@/assets/logo.png" alt="Logo" class="logo-img">
          </div>
        </div>

        <el-menu
          :default-active="activeMenu"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/admin">
            <el-icon><Odometer /></el-icon>
            <template #title>管理面板</template>
          </el-menu-item>

          <el-menu-item index="/admin/equipment">
            <el-icon><Monitor /></el-icon>
            <template #title>设备管理</template>
          </el-menu-item>

          <el-menu-item index="/admin/monitoring">
            <el-icon><DataAnalysis /></el-icon>
            <template #title>监测数据</template>
          </el-menu-item>

          <el-menu-item index="/admin/terrain">
            <el-icon><MapLocation /></el-icon>
            <template #title>地形数据</template>
          </el-menu-item>

          <el-menu-item v-if="authStore.isAdmin" index="/admin/users">
            <el-icon><User /></el-icon>
            <template #title>用户管理</template>
          </el-menu-item>

          <el-menu-item v-if="authStore.isAdmin" index="/admin/settings">
            <el-icon><Setting /></el-icon>
            <template #title>系统设置</template>
          </el-menu-item>

          <el-menu-item @click="goToVisualization">
            <el-icon><View /></el-icon>
            <template #title>可视化大屏</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              text
              :icon="sidebarCollapsed ? Expand : Fold"
              @click="toggleSidebar"
            />

            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <!-- WebSocket 连接状态 -->
            <el-tooltip :content="wsStatusText" placement="bottom">
              <el-badge
                :value="wsStatus === 'connected' ? '' : '!'"
                :type="wsStatus === 'connected' ? 'success' : 'danger'"
                :hidden="wsStatus === 'connected'"
              >
                <el-icon
                  :color="wsStatus === 'connected' ? '#67c23a' : '#f56c6c'"
                  size="20"
                >
                  <Connection />
                </el-icon>
              </el-badge>
            </el-tooltip>

            <!-- 可视化大屏 -->
            <el-tooltip content="可视化大屏" placement="bottom">
              <el-button
                type="primary"
                :icon="View"
                @click="goToVisualization"
              >
                可视化大屏
              </el-button>
            </el-tooltip>

            <!-- 全屏切换 -->
            <el-button
              text
              :icon="FullScreen"
              @click="toggleFullscreen"
            />

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="userAvatar">
                  {{ authStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ authStore.user?.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>

              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="changePassword">
                    <el-icon><Lock /></el-icon>
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="changePasswordVisible"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="changePasswordVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="authStore.isLoading"
          @click="handleChangePassword"
        >
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Odometer, Monitor, DataAnalysis, MapLocation, User, Setting,
  Expand, Fold, Connection, FullScreen,
  ArrowDown, Lock, SwitchButton, View
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { monitoringWebSocket } from '@/services/websocket'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const sidebarCollapsed = ref(false)
const isFullscreen = ref(false)
const changePasswordVisible = ref(false)
const passwordFormRef = ref<FormInstance>()

// 修改密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// WebSocket 状态
const wsStatus = computed(() => monitoringWebSocket.connectionStatus.value)
const wsStatusText = computed(() => {
  const statusMap = {
    connecting: '正在连接...',
    connected: '已连接',
    disconnected: '已断开',
    error: '连接错误'
  }
  return statusMap[wsStatus.value] || '未知状态'
})

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta?.title,
    path: item.path
  }))
})

// 用户头像
const userAvatar = computed(() => {
  // 这里可以根据用户信息生成头像URL
  return ''
})

// 表单验证规则
const passwordRules: FormRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 跳转到可视化大屏
const goToVisualization = () => {
  // 在新窗口中打开碳中和可视化大屏
  window.open('/#/carbon-neutral-bigscreen', '_blank')
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      ElMessage.info('个人资料功能开发中')
      break
    case 'changePassword':
      changePasswordVisible.value = true
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    await authStore.changePassword(passwordForm.oldPassword, passwordForm.newPassword)

    changePasswordVisible.value = false
    passwordForm.oldPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
  } catch (error) {
    console.error('Change password failed:', error)
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await authStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 组件挂载时
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 连接 WebSocket
  monitoringWebSocket.connect()
})

// 组件卸载时
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)

  // 断开 WebSocket
  monitoringWebSocket.disconnect()
})
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background: #304156;
  transition: width 0.3s;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #434a50;
}

.logo {
  display: flex;
  align-items: center;
  color: white;
}

.logo-collapsed {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo-img {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
}

.sidebar-menu {
  border: none;
  background: #304156;
}

:deep(.el-menu-item) {
  color: #bfcbd9;
}

:deep(.el-menu-item:hover) {
  background-color: #263445;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background-color: #409eff;
  color: white;
}

.header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}

.main-content {
  background: #f0f2f5;
  padding: 20px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #409eff;
}
</style>
