# 农业监测系统后端

基于 FastAPI 的农业监测系统后端服务，为 Vue3 + Cesium 前端项目提供数据支持。

## 功能特性

- 🌾 **设备管理**: 农业监测设备的增删改查和状态管理
- 📊 **数据采集**: 土壤、环境、设备状态等多维度监测数据
- 🗺️ **地形分析**: 地形采样数据的存储和分析
- 🔄 **实时通信**: WebSocket 支持实时数据推送
- 🤖 **数据模拟**: 自动生成模拟监测数据
- 📈 **统计分析**: 数据统计和趋势分析
- 🔍 **RESTful API**: 完整的 REST API 接口

## 技术栈

- **框架**: FastAPI 0.104+
- **数据库**: PostgreSQL (支持 SQLite 开发)
- **ORM**: SQLAlchemy 2.0+
- **数据验证**: Pydantic 2.0+
- **异步支持**: asyncio, uvicorn
- **实时通信**: WebSocket
- **数据分析**: NumPy, Pandas

## 项目结构

```
backend/
├── app/
│   ├── api/                    # API 路由
│   │   └── v1/
│   │       ├── endpoints/      # API 端点
│   │       └── api.py         # 路由汇总
│   ├── core/                   # 核心配置
│   │   ├── config.py          # 应用配置
│   │   └── database.py        # 数据库配置
│   ├── models/                 # 数据库模型
│   │   ├── equipment.py       # 设备模型
│   │   ├── monitoring_data.py # 监测数据模型
│   │   └── terrain_data.py    # 地形数据模型
│   ├── schemas/                # Pydantic 模式
│   │   ├── equipment.py       # 设备模式
│   │   ├── monitoring_data.py # 监测数据模式
│   │   └── terrain_data.py    # 地形数据模式
│   └── services/               # 业务服务
│       └── data_simulation.py # 数据模拟服务
├── scripts/
│   └── init_data.py           # 数据初始化脚本
├── main.py                    # 应用入口
├── requirements.txt           # 依赖包
└── README.md                  # 项目说明
```

## 快速开始

### 1. 环境准备

确保已安装 Python 3.8+:

```bash
python --version
```

### 2. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 3. 环境配置

复制环境配置文件:

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库等信息。

### 4. 初始化数据库

运行初始化脚本创建示例数据:

```bash
python scripts/init_data.py
```

### 5. 启动服务

```bash
python main.py
```

或使用 uvicorn:

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 6. 访问 API 文档

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

## API 接口

### 设备管理

- `GET /api/v1/equipment/` - 获取设备列表
- `GET /api/v1/equipment/{id}` - 获取单个设备
- `POST /api/v1/equipment/` - 创建设备
- `PUT /api/v1/equipment/{id}` - 更新设备
- `DELETE /api/v1/equipment/{id}` - 删除设备
- `GET /api/v1/equipment/status` - 获取设备状态统计

### 监测数据

- `GET /api/v1/monitoring-data/` - 获取监测数据列表
- `GET /api/v1/monitoring-data/latest` - 获取最新监测数据
- `GET /api/v1/monitoring-data/stats/{equipment_id}` - 获取设备数据统计
- `POST /api/v1/monitoring-data/` - 创建监测数据
- `POST /api/v1/monitoring-data/batch` - 批量创建监测数据

### 地形数据

- `GET /api/v1/terrain-data/` - 获取地形数据列表
- `GET /api/v1/terrain-data/{id}` - 获取单个地形数据
- `GET /api/v1/terrain-data/{id}/analysis` - 获取地形分析结果
- `POST /api/v1/terrain-data/` - 创建地形数据

### WebSocket

- `ws://localhost:8000/api/v1/ws/monitoring` - 监测数据实时推送
- `ws://localhost:8000/api/v1/ws/equipment/{id}` - 特定设备实时数据

## 数据模型

### 设备 (Equipment)

```json
{
  "id": 1,
  "name": "设备1",
  "equipment_id": "FARM_001",
  "longitude": 116.3974,
  "latitude": 39.9093,
  "altitude": 50.0,
  "status": "运行中",
  "battery_level": 85.0,
  "signal_strength": 92.0,
  "model": "AgriSensor Pro",
  "manufacturer": "农业科技有限公司",
  "firmware_version": "v2.1.0"
}
```

### 监测数据 (MonitoringData)

```json
{
  "id": 1,
  "equipment_id": 1,
  "soil_temperature": 22.5,
  "soil_moisture": 65.0,
  "soil_ph": 6.8,
  "ambient_temperature": 25.0,
  "ambient_humidity": 70.0,
  "light_intensity": 50000,
  "monitor_battery": 85.0,
  "measured_at": "2024-01-01T12:00:00"
}
```

## 开发指南

### 添加新的 API 端点

1. 在 `app/models/` 中定义数据模型
2. 在 `app/schemas/` 中定义 Pydantic 模式
3. 在 `app/api/v1/endpoints/` 中创建路由
4. 在 `app/api/v1/api.py` 中注册路由

### 数据模拟

系统内置数据模拟功能，会自动为激活的设备生成监测数据。可以通过环境变量控制:

```env
ENABLE_DATA_SIMULATION=True
SIMULATION_INTERVAL=30  # 秒
```

### WebSocket 使用

前端可以通过 WebSocket 接收实时数据更新:

```javascript
const ws = new WebSocket('ws://localhost:8000/api/v1/ws/monitoring');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到实时数据:', data);
};

// 订阅特定设备
ws.send(JSON.stringify({
    type: 'subscribe_equipment',
    equipment_id: 1
}));
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t nongye-backend .

# 运行容器
docker run -p 8000:8000 nongye-backend
```

### 生产环境

1. 设置环境变量 `ENVIRONMENT=production`
2. 配置 PostgreSQL 数据库
3. 使用 Gunicorn 或其他 WSGI 服务器
4. 配置反向代理 (Nginx)

## 许可证

MIT License
