export default {
  test: {
    test: 'test',
  },

  toolbar: {
    view: {
      content: 'View',
      flyTo: 'FlyTo',
      earth: 'Earth',
      china: 'China',
      statusbar: 'Status Bar',
      viewPosition: 'ViewPos',
      mousePosition: 'MousePos',
      fps: 'FPS',
      view: 'View',
      changeView: 'ViewMode',
      columbusViewMode: 'Columbus',
      cameraSetting: 'CameraSettting',
      areaLimit: 'AreaLimit',
      viewPosSampleDetail: 'Sample Rate(smaller more accurate)',
    },
    effect: {
      content: 'Effect',
      nature: 'Nature',
      sun: 'Sun',
      moon: 'Moon',
      atmosphere: 'Atmosphere',
      light: 'Light',
      sunLight: 'Sun',
      moonLight: 'Moon',
      cameraLigth: 'Camera',
      skybox: 'Skybox',
      shadow: 'Shadow',
      earthColor: 'EarthColor',
      uncoverColor: 'UncoverColor',
      imagery: 'Imagery',
      splitScreen: 'Split',
      splitPosition: 'Split Position',
      correctOffset: 'CorrectOffset',
      other: 'Other',
      depthTest: 'DepthTest',
    },
    tool: {
      content: 'Tool',
      d3DTile: '3DTiles',
      highLight: 'HighLight',
      classifyMove: 'Classify(Move)',
      classifyClick: 'Classify(Click)',
      draw: 'Draw(Left Start,Right End)',
      drawPoint: 'Point',
      drawLine: 'Line',
      drawPolygon: 'Polygon',
      removeDraw: 'Remove',
      measure: 'Measure',
      measurePoint: 'Point',
      measureLine: 'Line',
      measurePolygon: 'Polygon',
      removeMeasure: 'Remove',
      model: 'Model',
      clip: 'Clip',
    },
    terrainTool: {
      content: 'Terrain',
      analysis: 'Analysis',
      flood: 'Flood',
      sample: 'Sample',
      terrianSample: 'Terrain',
      remove: 'Remove',
      terrainColor: 'Terrain',
      elevationContour: 'Contour',
      contourDistance: 'Distance',
      contourWidth: 'Width',
      contourColor: 'Color',
    },
    other: {
      content: 'Other',
      cesium: 'Cesium',
      api: 'API Doc',
      demo: 'Cesium Demo',
      test: 'Test',
      setting: 'Setting',
    },
    industry: {
      content: 'Industry',
      home: 'Home',
    },
  },

  browserPanel: {
    imagery: {
      imageryManage: 'Imagery Manage',
      addImagery: 'Add Imagery (Dbclick)',
      alpha: 'Alpha',
      brightness: 'Brightness',
      contrast: 'Contrast',
      hue: 'Hue',
      saturation: 'Saturation',
    },
    primitive: {
      primitiveManage: 'Primitive Manage',
      add3DTilePrimitive: 'Add 3D Tileset',
      addPrimitiveName: 'Name',
      confirm: 'OK',
    },
    terrain: {
      terrainManage: 'Terrain Manage',
      terrainSetting: 'Terrain Setting',
      terrainScale: 'Scale',
      noTerrain: 'No Terrain',
      globeTerrain: 'Globe Terrain',
      testTerrain: 'Test Terrain(12m)',
    },
  },

  jtCameraSetting: {
    cameraSetting: 'Camera Setting',
    enableMinCameraDistance: 'Min Camera Distance',
    enableMaxCameraDistance: 'Max Camera Distance',
    enableRotate: 'Enable Rotate',
    enableTranslate: 'Enable Translate',
    enableZoom: 'Enable Zoom',
    enableTilt: 'Enable Tilt',
  },

  jtCartographicLimit: {
    title: 'Map Limit',
    east: 'East',
    west: 'West',
    north: 'North',
    south: 'South',
    currentViewExtend: 'CurrentView(3D only)',
    confirm: 'OK',
    reset: 'Reset',
  },

  jtFloodAnalysis: {
    title: 'Flood Analysis',
    drawArea: 'Click Draw Area',
    drawAreaDescription: '(Left start, Right end)',
    minHeight: 'Min Height',
    maxHeight: 'Max Height',
    animateTime: 'Animate Second',
    animateDetail: 'Animate Detail',
    startAnimate: 'Start',
  },

  jtImageryCorrectOffset: {
    title: 'Correct Offset',
    notNeed: '(Not Need)',
  },

  jtPrimitiveClipPlane: {
    title: 'Clipping Plane',
    clear: 'Clear',
    selectModel: 'Primitive',
    selectDir: 'Direction',
    minValue: 'Min Value',
    maxValue: 'max Value',
  },

  jtSetting: {
    title: 'Setting',
    toolbar: 'Toolbar',
    browserPanel: 'Browser Panel',
    logLonLatCoordOnClick: 'Log Coordi On Click',
    cesiumInspector: 'Cesium Inspector',
    d3dtileInspector: '3D Tiles Inspector',
  },

  locationBar: {
    view: 'View',
    mouse: 'Mouse',
  },

  jtTerrainSampleChart: {
    title: 'Terrain Sample',
  },
}
