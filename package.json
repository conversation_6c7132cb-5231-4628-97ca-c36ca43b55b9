{"name": "vue3-cesium-typescript-start-up-template", "version": "0.5.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "cesium": "^1.126.0", "core-js": "^3.26.1", "echarts": "^5.4.1", "element-plus": "^2.9.3", "fs-extra": "^11.1.0", "rollup-plugin-external-globals": "^0.7.1", "serve-static": "^1.15.0", "vue": "^3.2.45", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vuex": "^4.1.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^0.8.1", "@types/fs-extra": "^9.0.13", "@types/serve-static": "^1.15.0", "@vitejs/plugin-vue": "3.1.2", "@vue/runtime-core": "^3.2.45", "autoprefixer": "^10.4.13", "cz-conventional-changelog": "^3.3.0", "postcss": "^8.4.20", "rollup": "^3.7.3", "sass": "^1.56.2", "tailwindcss": "^3.2.4", "typescript": "^4.9.4", "unplugin-auto-import": "^0.12.0", "unplugin-vue-components": "^0.22.11", "vite": "3.1.8", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.0.13"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "packageManager": "pnpm@9.14.1+sha512.7f1de9cffea40ff4594c48a94776112a0db325e81fb18a9400362ff7b7247f4fbd76c3011611c9f8ac58743c3dc526017894e07948de9b72052f874ee2edfdcd"}