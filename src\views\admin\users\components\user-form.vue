<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="100px"
    :disabled="readonly"
  >
    <el-form-item label="用户名" prop="username">
      <el-input v-model="formData.username" placeholder="请输入用户名" />
    </el-form-item>
    
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="formData.email" placeholder="请输入邮箱" />
    </el-form-item>
    
    <el-form-item label="真实姓名" prop="full_name">
      <el-input v-model="formData.full_name" placeholder="请输入真实姓名（可选）" />
    </el-form-item>
    
    <el-form-item v-if="!user" label="密码" prop="password">
      <el-input
        v-model="formData.password"
        type="password"
        placeholder="请输入密码"
        show-password
      />
    </el-form-item>
    
    <el-form-item v-if="!user" label="确认密码" prop="confirmPassword">
      <el-input
        v-model="confirmPassword"
        type="password"
        placeholder="请确认密码"
        show-password
      />
    </el-form-item>
    
    <el-form-item label="用户角色" prop="is_superuser">
      <el-radio-group v-model="formData.is_superuser">
        <el-radio :label="false">普通用户</el-radio>
        <el-radio :label="true">管理员</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <el-form-item label="账号状态" prop="is_active">
      <el-switch
        v-model="formData.is_active"
        active-text="激活"
        inactive-text="禁用"
      />
    </el-form-item>
    
    <el-form-item v-if="user" label="邮箱验证" prop="is_verified">
      <el-switch
        v-model="formData.is_verified"
        active-text="已验证"
        inactive-text="未验证"
      />
    </el-form-item>
    
    <div v-if="user && readonly" class="user-stats">
      <el-divider content-position="left">用户统计</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic
            title="登录次数"
            :value="user.login_count"
            suffix="次"
          />
        </el-col>
        <el-col :span="8">
          <el-statistic
            title="最后登录"
            :value="user.last_login ? formatDateTime(user.last_login) : '从未登录'"
          />
        </el-col>
        <el-col :span="8">
          <el-statistic
            title="注册时间"
            :value="formatDateTime(user.created_at)"
          />
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { User } from '@/api/types'

interface Props {
  user?: User | null
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
  readonly: false
})

const emit = defineEmits<{
  submit: [data: any]
}>()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  full_name: '',
  password: '',
  is_superuser: false,
  is_active: true,
  is_verified: false
})

const confirmPassword = ref('')

// 自定义验证器
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value !== formData.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: !props.user, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: !props.user, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 监听用户数据变化
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      // 编辑模式，填充表单数据
      Object.assign(formData, {
        username: newUser.username,
        email: newUser.email,
        full_name: newUser.full_name || '',
        password: '',
        is_superuser: newUser.is_superuser,
        is_active: newUser.is_active,
        is_verified: newUser.is_verified
      })
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  },
  { immediate: true }
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    full_name: '',
    password: '',
    is_superuser: false,
    is_active: true,
    is_verified: false
  })
  confirmPassword.value = ''
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 验证表单
const validate = async () => {
  if (!formRef.value) throw new Error('表单引用不存在')
  
  try {
    await formRef.value.validate()
    
    // 处理空字符串为 undefined
    const submitData = { ...formData }
    if (!submitData.full_name) {
      submitData.full_name = undefined as any
    }
    
    // 如果是编辑模式且密码为空，则不包含密码字段
    if (props.user && !submitData.password) {
      delete (submitData as any).password
    }
    
    return submitData
  } catch (error) {
    ElMessage.error('请检查表单输入')
    throw error
  }
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm
})
</script>

<style scoped>
.user-stats {
  margin-top: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-statistic__content) {
  font-size: 16px;
}

:deep(.el-statistic__number) {
  color: #409eff;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #303133;
}
</style>
