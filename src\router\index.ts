import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import Home from '@/views/home'
import IndustryPanels from '@/views/home/<USER>'
import CarbonNeutalBigscreen from '@/views/home/<USER>/carbon-neutral-bigscreen'
import Test from '@/views/test/test.vue'
import Bigscreen from '@/views/home/<USER>/bigscreen.vue'

// const routes: Array<RouteRecordRaw> = [
//   {
//     path: '/',
//     name: 'Home',
//     component: Home,
//   },
// ]

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    redirect:'/carbon-neutral-bigscreen',
    meta: {
      title: '农业监测系统',
      requiresAuth: false
    },
    children: [
      {
        path: 'industry',
        name: 'industry',
        component: IndustryPanels,
        meta: {
          title: '产业面板',
          requiresAuth: false
        }
      },
      {
        path: 'carbon-neutral-bigscreen',
        name: 'carbon-neutral-bigscreen',
        component: CarbonNeutalBigscreen,
        meta: {
          title: '碳中和大屏',
          requiresAuth: false
        }
      },
      {
        path: 'bigscreen',
        name: 'bigscreen',
        component: Bigscreen,
        meta: {
          title: '大屏展示',
          requiresAuth: false
        }
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/login.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/views/auth/register.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideForAuth: true
    }
  },
  {
    path: '/admin',
    name: 'admin',
    component: () => import('@/views/admin/layout.vue'),
    meta: {
      title: '后台管理',
      requiresAuth: true,
      requiresAdmin: true
    },
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: () => import('@/views/admin/dashboard/index.vue'),
        meta: {
          title: '管理面板',
          requiresAuth: true
        }
      },
      {
        path: 'equipment',
        name: 'admin-equipment',
        component: () => import('@/views/admin/equipment/index.vue'),
        meta: {
          title: '设备管理',
          requiresAuth: true
        }
      },
      {
        path: 'monitoring',
        name: 'admin-monitoring',
        component: () => import('@/views/admin/monitoring/index.vue'),
        meta: {
          title: '监测数据',
          requiresAuth: true
        }
      },
      {
        path: 'terrain',
        name: 'admin-terrain',
        component: () => import('@/views/admin/terrain/index.vue'),
        meta: {
          title: '地形数据',
          requiresAuth: true
        }
      },
      {
        path: 'users',
        name: 'admin-users',
        component: () => import('@/views/admin/users/index.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          requiresAdmin: true
        }
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: () => import('@/views/admin/settings/index.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true,
          requiresAdmin: true
        }
      }
    ]
  },
  {
    path: '/test',
    name: 'test',
    component: Test,
    meta: {
      title: '测试页面',
      requiresAuth: false
    }
  },
  {
    path: '/403',
    name: 'forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '访问被拒绝',
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: 'not-found',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  // history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 如果用户已登录且访问登录/注册页面，重定向到首页
  if (authStore.isAuthenticated && to.meta.hideForAuth) {
    next('/')
    return
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    next('/login')
    return
  }

  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !authStore.isAdmin) {
    ElMessage.error('没有权限访问此页面')
    next('/403')
    return
  }

  // 检查路由权限
  if (!authStore.canAccessRoute(to.meta)) {
    ElMessage.error('没有权限访问此页面')
    next('/403')
    return
  }

  next()
})

export default router
