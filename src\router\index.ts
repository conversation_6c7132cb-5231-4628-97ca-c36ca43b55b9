import { createRouter, createWebHistory, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import Home from '@/views/home'
import IndustryPanels from '@/views/home/<USER>'
import CarbonNeutalBigscreen from '@/views/home/<USER>/carbon-neutral-bigscreen'
import Test from '@/views/test/test.vue'
import Bigscreen from '@/views/home/<USER>/bigscreen.vue'

// const routes: Array<RouteRecordRaw> = [
//   {
//     path: '/',
//     name: 'Home',
//     component: Home,
//   },
// ]

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    redirect:'/carbon-neutral-bigscreen',
    children: [
      {
        path: 'industry',
        name: 'industry',
        component: IndustryPanels,
      },
      {
        path: 'carbon-neutral-bigscreen',
        name: 'carbon-neutral-bigscreen',
        component: CarbonNeutalBigscreen,
      },
      {
        path: 'bigscreen',
        name: 'bigscreen',
        component: Bigscreen,
      },
    ],
  },
  {
    path: '/test',
    name: 'test',
    component: Test,
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  // history: createWebHistory(),
  routes,
})

export default router
