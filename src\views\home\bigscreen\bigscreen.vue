<template>
  <div class="bigscreen fixed top-0 bottom-0 left-0 right-0 overflow-hidden flex flex-col">
    <div
      class="header px-6 h-20 flex-grow-0 flex-shrink-0 pointer-events-auto flex flex-row items-center justify-between"
    >
      <div class="title text-white font-bold text-2xl">数字农田</div>
      <button class="back-btn" @click="goBack">返回管理后台</button>
    </div>
    <div class="bigscreen-body">
      <div class="bigscreen-left"></div>
      <div class="bigscreen-right"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import nowDate from '@/views/home/<USER>/carbon-neutral-bigscreen/components/now-date.vue'

export default defineComponent({
  name: 'bigscreen',
  components: { nowDate },
  props: {},
  setup(props, context) {
    const goBack = () => {
      // 关闭当前窗口或返回管理后台
      if (window.opener) {
        window.close()
      } else {
        window.location.href = '/#/admin'
      }
    }

    return { goBack }
  }
})
</script>

<style lang="scss" scoped>
.bigscreen {
  &-header {
    z-index: 999;
    width: 100%;
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-image: url('static/imgs/echarts/header.png');
    background-size: contain;

    .title {
      margin-left: 20px;
    }

    .back-btn {
      margin-right: 20px;
      padding: 8px 16px;
      background: rgba(64, 158, 255, 0.2);
      border: 1px solid #409eff;
      border-radius: 6px;
      color: #409eff;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.3);
        box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
      }
    }
  }

  &-body {
    width: 100%;
    flex: 1;
    // background-color: red;
    display: flex;
    justify-content: space-between;
    padding-top: 5px;
  }

  &-left {
    //z-index: 999;
    width: 30%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
  }
}
</style>
