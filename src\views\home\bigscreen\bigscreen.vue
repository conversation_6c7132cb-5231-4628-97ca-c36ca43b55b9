<template>
  <div class="bigscreen">
    <!-- 头部标题 -->
    <div class="bigscreen-header">
      <div class="header-left">
        <div class="title">农业监测可视化大屏</div>
      </div>
      <div class="header-right">
        <div class="time">{{ currentTime }}</div>
        <button class="back-btn" @click="goBack">返回管理后台</button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="bigscreen-body">
      <!-- 左侧面板 -->
      <div class="bigscreen-left">
        <div class="panel">
          <div class="panel-title">设备状态统计</div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ equipmentStats.total }}</div>
              <div class="stat-label">总设备数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ equipmentStats.online }}</div>
              <div class="stat-label">在线设备</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ equipmentStats.offline }}</div>
              <div class="stat-label">离线设备</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ equipmentStats.warning }}</div>
              <div class="stat-label">告警设备</div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-title">实时监测数据</div>
          <div class="monitor-list">
            <div v-for="item in latestData" :key="item.id" class="monitor-item">
              <div class="monitor-name">{{ item.name }}</div>
              <div class="monitor-values">
                <span>温度: {{ item.temperature }}°C</span>
                <span>湿度: {{ item.humidity }}%</span>
                <span>pH: {{ item.ph }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间地图区域 -->
      <div class="bigscreen-center">
        <div class="map-container">
          <div id="cesium-container" class="cesium-container"></div>
          <div class="map-overlay">
            <div class="overlay-title">农田分布图</div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="bigscreen-right">
        <div class="panel">
          <div class="panel-title">环境指标</div>
          <div class="env-indicators">
            <div class="indicator">
              <div class="indicator-label">平均温度</div>
              <div class="indicator-value">{{ avgTemperature }}°C</div>
            </div>
            <div class="indicator">
              <div class="indicator-label">平均湿度</div>
              <div class="indicator-value">{{ avgHumidity }}%</div>
            </div>
            <div class="indicator">
              <div class="indicator-label">光照强度</div>
              <div class="indicator-value">{{ avgLight }} lux</div>
            </div>
            <div class="indicator">
              <div class="indicator-label">风速</div>
              <div class="indicator-value">{{ avgWindSpeed }} m/s</div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-title">告警信息</div>
          <div class="alert-list">
            <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="alert.level">
              <div class="alert-time">{{ formatTime(alert.time) }}</div>
              <div class="alert-message">{{ alert.message }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { equipmentApi } from '@/api/equipment'
import { monitoringApi } from '@/api/monitoring'

// 响应式数据
const currentTime = ref('')
const equipmentStats = reactive({
  total: 0,
  online: 0,
  offline: 0,
  warning: 0
})

const latestData = ref([])
const alerts = ref([
  {
    id: 1,
    time: new Date(),
    message: '设备001温度异常',
    level: 'warning'
  },
  {
    id: 2,
    time: new Date(),
    message: '设备003离线',
    level: 'error'
  }
])

// 计算属性
const avgTemperature = computed(() => {
  if (latestData.value.length === 0) return '0.0'
  const sum = latestData.value.reduce((acc, item) => acc + item.temperature, 0)
  return (sum / latestData.value.length).toFixed(1)
})

const avgHumidity = computed(() => {
  if (latestData.value.length === 0) return '0.0'
  const sum = latestData.value.reduce((acc, item) => acc + item.humidity, 0)
  return (sum / latestData.value.length).toFixed(1)
})

const avgLight = computed(() => {
  if (latestData.value.length === 0) return '0'
  const sum = latestData.value.reduce((acc, item) => acc + (item.light_intensity || 0), 0)
  return Math.round(sum / latestData.value.length)
})

const avgWindSpeed = computed(() => {
  if (latestData.value.length === 0) return '0.0'
  const sum = latestData.value.reduce((acc, item) => acc + (item.wind_speed || 0), 0)
  return (sum / latestData.value.length).toFixed(1)
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatTime = (time: Date) => {
  return time.toLocaleTimeString('zh-CN')
}

const goBack = () => {
  // 关闭当前窗口或返回管理后台
  if (window.opener) {
    window.close()
  } else {
    window.location.href = '/#/admin'
  }
}

const loadEquipmentStats = async () => {
  try {
    const equipment = await equipmentApi.getEquipmentList()
    equipmentStats.total = equipment.length
    equipmentStats.online = equipment.filter(e => e.status === '运行中').length
    equipmentStats.offline = equipment.filter(e => e.status === '离线').length
    equipmentStats.warning = equipment.filter(e => e.status === '故障').length
  } catch (error) {
    console.error('Failed to load equipment stats:', error)
  }
}

const loadLatestData = async () => {
  try {
    const data = await monitoringApi.getLatestData()
    latestData.value = data.map(item => ({
      id: item.id,
      name: `设备${item.equipment_id.toString().padStart(3, '0')}`,
      temperature: item.soil_temperature,
      humidity: item.soil_moisture,
      ph: item.soil_ph,
      light_intensity: item.light_intensity,
      wind_speed: item.wind_speed
    }))
  } catch (error) {
    console.error('Failed to load latest data:', error)
  }
}

// 定时器
let timeTimer: number
let dataTimer: number

// 生命周期
onMounted(() => {
  // 更新时间
  updateTime()
  timeTimer = setInterval(updateTime, 1000)

  // 加载数据
  loadEquipmentStats()
  loadLatestData()

  // 定时刷新数据
  dataTimer = setInterval(() => {
    loadEquipmentStats()
    loadLatestData()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer)
  if (dataTimer) clearInterval(dataTimer)
})
</script>

<style lang="scss" scoped>
.bigscreen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
  color: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-header {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    background: linear-gradient(90deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
    border-bottom: 2px solid rgba(64, 158, 255, 0.3);

    .header-left {
      .title {
        font-size: 32px;
        font-weight: bold;
        color: #409eff;
        text-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 20px;

      .time {
        font-size: 18px;
        color: #67c23a;
        font-family: 'Courier New', monospace;
      }

      .back-btn {
        padding: 8px 16px;
        background: rgba(64, 158, 255, 0.2);
        border: 1px solid #409eff;
        border-radius: 6px;
        color: #409eff;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(64, 158, 255, 0.3);
          box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
        }
      }
    }
  }

  &-body {
    flex: 1;
    display: flex;
    gap: 20px;
    padding: 20px;
  }

  &-left,
  &-right {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &-center {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.panel {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);

  .panel-title {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 1px solid rgba(64, 158, 255, 0.3);
    padding-bottom: 10px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;

  .stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(64, 158, 255, 0.2);

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #67c23a;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 12px;
      color: #bfcbd9;
    }
  }
}

.monitor-list {
  max-height: 200px;
  overflow-y: auto;

  .monitor-item {
    padding: 10px;
    margin-bottom: 10px;
    background: rgba(103, 194, 58, 0.1);
    border-radius: 6px;
    border-left: 3px solid #67c23a;

    .monitor-name {
      font-weight: bold;
      color: #67c23a;
      margin-bottom: 5px;
    }

    .monitor-values {
      display: flex;
      flex-direction: column;
      gap: 2px;

      span {
        font-size: 12px;
        color: #bfcbd9;
      }
    }
  }
}

.env-indicators {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(245, 108, 108, 0.1);
    border-radius: 6px;
    border-left: 3px solid #f56c6c;

    .indicator-label {
      color: #bfcbd9;
      font-size: 14px;
    }

    .indicator-value {
      color: #f56c6c;
      font-weight: bold;
      font-size: 16px;
    }
  }
}

.alert-list {
  max-height: 200px;
  overflow-y: auto;

  .alert-item {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 6px;
    border-left: 3px solid;

    &.warning {
      background: rgba(230, 162, 60, 0.1);
      border-left-color: #e6a23c;

      .alert-time {
        color: #e6a23c;
      }
    }

    &.error {
      background: rgba(245, 108, 108, 0.1);
      border-left-color: #f56c6c;

      .alert-time {
        color: #f56c6c;
      }
    }

    .alert-time {
      font-size: 12px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .alert-message {
      font-size: 14px;
      color: #bfcbd9;
    }
  }
}

.map-container {
  flex: 1;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;

  .cesium-container {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #1a2332 0%, #2c3e50 100%);
  }

  .map-overlay {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;

    .overlay-title {
      font-size: 20px;
      font-weight: bold;
      color: #409eff;
      text-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.7);
}
</style>
