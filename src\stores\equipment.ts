import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { equipmentApi } from '@/api/equipment'
import type { Equipment, EquipmentCreate, EquipmentUpdate, EquipmentStatus } from '@/api/types'

export const useEquipmentStore = defineStore('equipment', () => {
  // 状态
  const equipmentList = ref<Equipment[]>([])
  const currentEquipment = ref<Equipment | null>(null)
  const equipmentStatus = ref<EquipmentStatus | null>(null)
  const isLoading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const activeEquipment = computed(() => 
    equipmentList.value.filter(eq => eq.is_active && eq.status === '运行中')
  )
  
  const offlineEquipment = computed(() => 
    equipmentList.value.filter(eq => eq.status === '离线')
  )
  
  const lowBatteryEquipment = computed(() => 
    equipmentList.value.filter(eq => (eq.battery_level || 0) < 20)
  )

  // 获取设备列表
  const fetchEquipmentList = async (params?: {
    page?: number
    size?: number
    status?: string
    is_active?: boolean
    search?: string
  }) => {
    isLoading.value = true
    try {
      const response = await equipmentApi.getEquipmentList(params)
      equipmentList.value = response.items
      total.value = response.total
      currentPage.value = response.page
      pageSize.value = response.size
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '获取设备列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取设备状态统计
  const fetchEquipmentStatus = async () => {
    try {
      equipmentStatus.value = await equipmentApi.getEquipmentStatus()
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '获取设备状态失败')
      throw error
    }
  }

  // 获取单个设备
  const fetchEquipment = async (id: number) => {
    isLoading.value = true
    try {
      currentEquipment.value = await equipmentApi.getEquipment(id)
      return currentEquipment.value
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '获取设备信息失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 创建设备
  const createEquipment = async (equipment: EquipmentCreate) => {
    isLoading.value = true
    try {
      const newEquipment = await equipmentApi.createEquipment(equipment)
      equipmentList.value.unshift(newEquipment)
      total.value++
      ElMessage.success('设备创建成功')
      return newEquipment
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '设备创建失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 更新设备
  const updateEquipment = async (id: number, equipment: EquipmentUpdate) => {
    isLoading.value = true
    try {
      const updatedEquipment = await equipmentApi.updateEquipment(id, equipment)
      
      // 更新列表中的设备
      const index = equipmentList.value.findIndex(eq => eq.id === id)
      if (index !== -1) {
        equipmentList.value[index] = updatedEquipment
      }
      
      // 更新当前设备
      if (currentEquipment.value?.id === id) {
        currentEquipment.value = updatedEquipment
      }
      
      ElMessage.success('设备更新成功')
      return updatedEquipment
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '设备更新失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 删除设备
  const deleteEquipment = async (id: number) => {
    isLoading.value = true
    try {
      await equipmentApi.deleteEquipment(id)
      
      // 从列表中移除
      const index = equipmentList.value.findIndex(eq => eq.id === id)
      if (index !== -1) {
        equipmentList.value.splice(index, 1)
        total.value--
      }
      
      // 清除当前设备
      if (currentEquipment.value?.id === id) {
        currentEquipment.value = null
      }
      
      ElMessage.success('设备删除成功')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '设备删除失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 批量删除设备
  const batchDeleteEquipment = async (ids: number[]) => {
    isLoading.value = true
    try {
      await equipmentApi.batchDeleteEquipment(ids)
      
      // 从列表中移除
      equipmentList.value = equipmentList.value.filter(eq => !ids.includes(eq.id))
      total.value -= ids.length
      
      ElMessage.success(`成功删除 ${ids.length} 个设备`)
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '批量删除失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 导出设备数据
  const exportEquipment = async (params?: {
    status?: string
    is_active?: boolean
    search?: string
  }) => {
    try {
      const blob = await equipmentApi.exportEquipment(params)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `equipment_${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('设备数据导出成功')
    } catch (error: any) {
      ElMessage.error(error.response?.data?.message || '设备数据导出失败')
      throw error
    }
  }

  // 根据ID查找设备
  const findEquipmentById = (id: number): Equipment | undefined => {
    return equipmentList.value.find(eq => eq.id === id)
  }

  // 清除当前设备
  const clearCurrentEquipment = () => {
    currentEquipment.value = null
  }

  return {
    // 状态
    equipmentList,
    currentEquipment,
    equipmentStatus,
    isLoading,
    total,
    currentPage,
    pageSize,
    
    // 计算属性
    activeEquipment,
    offlineEquipment,
    lowBatteryEquipment,
    
    // 方法
    fetchEquipmentList,
    fetchEquipmentStatus,
    fetchEquipment,
    createEquipment,
    updateEquipment,
    deleteEquipment,
    batchDeleteEquipment,
    exportEquipment,
    findEquipmentById,
    clearCurrentEquipment,
  }
})
