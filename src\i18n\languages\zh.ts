export default {
  test: {
    test: '测试',
  },

  toolbar: {
    view: {
      content: '视图',
      flyTo: '定位到',
      earth: '全球',
      china: '中国',
      statusbar: '状态栏',
      viewPosition: '视角坐标',
      mousePosition: '鼠标坐标',
      fps: 'FPS',
      view: '视图',
      changeView: '视图模式',
      columbusViewMode: '哥伦布',
      cameraSetting: '相机设置',
      areaLimit: '范围限制',
      viewPosSampleDetail: '采样率(越小越精确)',
    },
    effect: {
      content: '效果',
      nature: '自然环境',
      sun: '太阳',
      moon: '月亮',
      atmosphere: '大气层',
      light: '日照',
      sunLight: '太阳光',
      moonLight: '月光',
      cameraLigth: '视角光',
      skybox: '天空盒',
      shadow: '阴影',
      earthColor: '地球颜色',
      uncoverColor: '无覆盖时颜色',
      imagery: '影像',
      splitScreen: '分屏',
      splitPosition: '分屏位置设置',
      correctOffset: '偏移纠正',
      other: '其他',
      depthTest: '深度检测',
    },
    tool: {
      content: '工具',
      d3DTile: '3DTiles',
      highLight: '高亮',
      classifyMove: '分类(滑动)',
      classifyClick: '分类(点击)',
      draw: '绘制(左键开始,右键结束)',
      drawPoint: '画点',
      drawLine: '画线',
      drawPolygon: '画面',
      removeDraw: '移除',
      measure: '测量',
      measurePoint: '点',
      measureLine: '线',
      measurePolygon: '面',
      removeMeasure: '移除',
      model: '模型',
      clip: '剖分',
    },
    terrainTool: {
      content: '地形',
      analysis: '分析',
      flood: '淹没分析',
      sample: '采样',
      terrianSample: '地形采样',
      remove: '移除',
      terrainColor: '地形着色',
      elevationContour: '等高线',
      contourDistance: '等高距',
      contourWidth: '线宽',
      contourColor: '颜色',
    },
    other: {
      content: '其他',
      cesium: 'Cesium',
      api: 'API文档',
      demo: '官方Demo',
      test: 'test',
      setting: '设置',
    },
    industry: {
      content: '应用',
      home: '主页',
    },
  },

  browserPanel: {
    imagery: {
      imageryManage: '影像管理',
      addImagery: '添加影像（双击）',
      alpha: 'Alpha',
      brightness: '亮度',
      contrast: '对比度',
      hue: '色调',
      saturation: '饱和度',
    },
    primitive: {
      primitiveManage: '模型管理',
      add3DTilePrimitive: '添加 3D Tileset 模型',
      addPrimitiveName: '名称',
      confirm: '确定',
    },
    terrain: {
      terrainManage: '地形管理',
      terrainSetting: '地形设置',
      terrainScale: '地形缩放',
      noTerrain: '无地形',
      globeTerrain: '全球简略地形',
      testTerrain: '测试001-地形(12m)',
    },
  },

  jtCameraSetting: {
    cameraSetting: '相机设置',
    enableMinCameraDistance: '最小镜头距离设置',
    enableMaxCameraDistance: '最大镜头距离设置',
    enableRotate: '启动相机旋转',
    enableTranslate: '启动相机平移',
    enableZoom: '启动相机缩放',
    enableTilt: '启动相机倾斜',
  },

  jtCartographicLimit: {
    title: '地图范围限制',
    east: '东经',
    west: '西经',
    north: '北纬',
    south: '南纬',
    currentViewExtend: '当前视口范围(3D模式有效)',
    confirm: '确定',
    reset: '重置',
  },

  jtFloodAnalysis: {
    title: '淹没分析',
    drawArea: '点击绘制范围',
    drawAreaDescription: '(左键开始,右键结束)',
    minHeight: '最小高度',
    maxHeight: '最大高度',
    animateTime: '动画时长',
    animateDetail: '动画细粒度',
    startAnimate: '开始动画',
  },

  jtImageryCorrectOffset: {
    title: '偏移纠正',
    notNeed: '(无须纠正)',
  },

  jtPrimitiveClipPlane: {
    title: '模型剖分',
    clear: '全部清除',
    selectModel: '选择模型',
    selectDir: '选择方向',
    minValue: '最小值',
    maxValue: '最大值',
  },

  jtSetting: {
    title: '设置',
    toolbar: '工具栏',
    browserPanel: '数据列表',
    logLonLatCoordOnClick: '点击输出鼠标经纬度',
    cesiumInspector: 'Cesium调试器',
    d3dtileInspector: '3D Tiles调试器',
  },

  locationBar: {
    view: '视角',
    mouse: '鼠标',
  },

  jtTerrainSampleChart: {
    title: '地形采样',
  },
}
