from typing import List, Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session
import json
import asyncio
from datetime import datetime

from app.core.database import get_db
from app.models.monitoring_data import MonitoringData as MonitoringDataModel
from app.models.equipment import Equipment as EquipmentModel

router = APIRouter()

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.equipment_subscribers: Dict[int, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # 从设备订阅中移除
        for equipment_id, subscribers in self.equipment_subscribers.items():
            if websocket in subscribers:
                subscribers.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except:
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_to_equipment_subscribers(self, equipment_id: int, message: str):
        """发送消息给特定设备的订阅者"""
        if equipment_id in self.equipment_subscribers:
            disconnected = []
            for connection in self.equipment_subscribers[equipment_id]:
                try:
                    await connection.send_text(message)
                except:
                    disconnected.append(connection)
            
            # 清理断开的连接
            for connection in disconnected:
                if connection in self.equipment_subscribers[equipment_id]:
                    self.equipment_subscribers[equipment_id].remove(connection)
    
    def subscribe_to_equipment(self, equipment_id: int, websocket: WebSocket):
        """订阅特定设备的数据更新"""
        if equipment_id not in self.equipment_subscribers:
            self.equipment_subscribers[equipment_id] = []
        
        if websocket not in self.equipment_subscribers[equipment_id]:
            self.equipment_subscribers[equipment_id].append(websocket)

# 创建连接管理器实例
manager = ConnectionManager()

@router.websocket("/monitoring")
async def websocket_monitoring_endpoint(websocket: WebSocket):
    """监测数据WebSocket端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理不同类型的消息
            if message_data.get("type") == "subscribe_equipment":
                equipment_id = message_data.get("equipment_id")
                if equipment_id:
                    manager.subscribe_to_equipment(equipment_id, websocket)
                    await manager.send_personal_message(
                        json.dumps({
                            "type": "subscription_confirmed",
                            "equipment_id": equipment_id,
                            "message": f"已订阅设备 {equipment_id} 的数据更新"
                        }),
                        websocket
                    )
            
            elif message_data.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}),
                    websocket
                )
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@router.websocket("/equipment/{equipment_id}")
async def websocket_equipment_endpoint(websocket: WebSocket, equipment_id: int):
    """特定设备的WebSocket端点"""
    await manager.connect(websocket)
    manager.subscribe_to_equipment(equipment_id, websocket)
    
    try:
        # 发送欢迎消息
        await manager.send_personal_message(
            json.dumps({
                "type": "connected",
                "equipment_id": equipment_id,
                "message": f"已连接到设备 {equipment_id} 的实时数据流"
            }),
            websocket
        )
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data.get("type") == "ping":
                await manager.send_personal_message(
                    json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}),
                    websocket
                )
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# 用于发送实时数据更新的辅助函数
async def broadcast_monitoring_data_update(monitoring_data: Dict[str, Any]):
    """广播监测数据更新"""
    message = json.dumps({
        "type": "monitoring_data_update",
        "data": monitoring_data,
        "timestamp": datetime.now().isoformat()
    })
    
    # 广播给所有连接
    await manager.broadcast(message)
    
    # 发送给特定设备的订阅者
    equipment_id = monitoring_data.get("equipment_id")
    if equipment_id:
        await manager.send_to_equipment_subscribers(equipment_id, message)

async def broadcast_equipment_status_update(equipment_data: Dict[str, Any]):
    """广播设备状态更新"""
    message = json.dumps({
        "type": "equipment_status_update",
        "data": equipment_data,
        "timestamp": datetime.now().isoformat()
    })
    
    # 广播给所有连接
    await manager.broadcast(message)
    
    # 发送给特定设备的订阅者
    equipment_id = equipment_data.get("id")
    if equipment_id:
        await manager.send_to_equipment_subscribers(equipment_id, message)

# 导出管理器和广播函数，供其他模块使用
__all__ = ["manager", "broadcast_monitoring_data_update", "broadcast_equipment_status_update"]
