<template>
  <div class="monitoring-detail">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="设备名称">
        {{ equipmentName }}
      </el-descriptions-item>
      <el-descriptions-item label="测量时间">
        {{ formatDateTime(data.measured_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="数据质量">
        <el-tag :type="data.data_quality === 'good' ? 'success' : 'warning'">
          {{ data.data_quality === 'good' ? '良好' : '警告' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDateTime(data.created_at) }}
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="left">土壤数据</el-divider>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-statistic
          title="土壤温度"
          :value="data.soil_temperature || 0"
          suffix="°C"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="土壤湿度"
          :value="data.soil_moisture || 0"
          suffix="%"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="土壤pH值"
          :value="data.soil_ph || 0"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-statistic
          title="土壤电导率"
          :value="data.soil_conductivity || 0"
          suffix="mS/cm"
          :precision="2"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="土壤氮含量"
          :value="data.soil_nitrogen || 0"
          suffix="mg/kg"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="土壤磷含量"
          :value="data.soil_phosphorus || 0"
          suffix="mg/kg"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-statistic
          title="土壤钾含量"
          :value="data.soil_potassium || 0"
          suffix="mg/kg"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-divider content-position="left">环境数据</el-divider>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-statistic
          title="环境温度"
          :value="data.ambient_temperature || 0"
          suffix="°C"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="环境湿度"
          :value="data.ambient_humidity || 0"
          suffix="%"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="光照强度"
          :value="data.light_intensity || 0"
          suffix="lux"
          :precision="0"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-statistic
          title="紫外线指数"
          :value="data.uv_index || 0"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="风速"
          :value="data.wind_speed || 0"
          suffix="m/s"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="风向"
          :value="data.wind_direction || 0"
          suffix="°"
          :precision="0"
        />
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-statistic
          title="大气压强"
          :value="data.atmospheric_pressure || 0"
          suffix="hPa"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="降雨量"
          :value="data.rainfall || 0"
          suffix="mm"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-divider content-position="left">设备状态</el-divider>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-statistic
          title="设备电量"
          :value="data.monitor_battery || 0"
          suffix="%"
          :precision="1"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="信号质量"
          :value="data.signal_quality || 0"
          suffix="%"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <el-divider content-position="left">位置信息</el-divider>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-statistic
          title="经度"
          :value="data.longitude || 0"
          :precision="6"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="纬度"
          :value="data.latitude || 0"
          :precision="6"
        />
      </el-col>
      <el-col :span="8">
        <el-statistic
          title="海拔"
          :value="data.altitude || 0"
          suffix="m"
          :precision="1"
        />
      </el-col>
    </el-row>
    
    <div v-if="data.error_code || data.error_message" style="margin-top: 20px;">
      <el-divider content-position="left">错误信息</el-divider>
      <el-alert
        :title="data.error_code || '未知错误'"
        :description="data.error_message"
        type="error"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MonitoringData } from '@/api/types'

interface Props {
  data: MonitoringData
  equipmentName: string
}

defineProps<Props>()

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.monitoring-detail {
  padding: 10px 0;
}

:deep(.el-statistic__content) {
  font-size: 18px;
}

:deep(.el-statistic__number) {
  color: #409eff;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #303133;
}
</style>
