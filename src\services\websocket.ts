import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { WebSocketMessage } from '@/api/types'

export interface WebSocketOptions {
  url: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  onMessage?: (message: WebSocketMessage) => void
  onOpen?: () => void
  onClose?: () => void
  onError?: (error: Event) => void
}

export class WebSocketService {
  private ws: WebSocket | null = null
  private options: WebSocketOptions
  private reconnectAttempts = 0
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null
  
  public isConnected = ref(false)
  public connectionStatus = ref<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  public lastMessage = ref<WebSocketMessage | null>(null)
  public messageHistory = reactive<WebSocketMessage[]>([])

  constructor(options: WebSocketOptions) {
    this.options = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      ...options,
    }
  }

  connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return
    }

    this.connectionStatus.value = 'connecting'
    
    try {
      this.ws = new WebSocket(this.options.url)
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.connectionStatus.value = 'error'
    }
  }

  disconnect(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.isConnected.value = false
    this.connectionStatus.value = 'disconnected'
    this.reconnectAttempts = 0
  }

  send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
      ElMessage.warning('WebSocket 连接已断开，无法发送消息')
    }
  }

  // 订阅设备数据更新
  subscribeToEquipment(equipmentId: number): void {
    this.send({
      type: 'subscribe_equipment',
      equipment_id: equipmentId,
    })
  }

  // 发送心跳
  private sendHeartbeat(): void {
    this.send({
      type: 'ping',
      timestamp: new Date().toISOString(),
    })
  }

  private handleOpen(): void {
    console.log('WebSocket connected')
    this.isConnected.value = true
    this.connectionStatus.value = 'connected'
    this.reconnectAttempts = 0
    
    // 启动心跳
    if (this.options.heartbeatInterval) {
      this.heartbeatTimer = setInterval(() => {
        this.sendHeartbeat()
      }, this.options.heartbeatInterval)
    }
    
    this.options.onOpen?.()
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // 更新最新消息
      this.lastMessage.value = message
      
      // 添加到消息历史（保留最近100条）
      this.messageHistory.unshift(message)
      if (this.messageHistory.length > 100) {
        this.messageHistory.splice(100)
      }
      
      // 处理特定消息类型
      switch (message.type) {
        case 'pong':
          // 心跳响应，不需要特殊处理
          break
        case 'monitoring_data_update':
          console.log('收到监测数据更新:', message.data)
          break
        case 'equipment_status_update':
          console.log('收到设备状态更新:', message.data)
          break
        case 'subscription_confirmed':
          ElMessage.success(message.message || '订阅成功')
          break
        default:
          console.log('收到未知类型消息:', message)
      }
      
      this.options.onMessage?.(message)
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  private handleClose(): void {
    console.log('WebSocket disconnected')
    this.isConnected.value = false
    this.connectionStatus.value = 'disconnected'
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    this.options.onClose?.()
    
    // 尝试重连
    this.attemptReconnect()
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error)
    this.connectionStatus.value = 'error'
    this.options.onError?.(error)
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= (this.options.maxReconnectAttempts || 5)) {
      console.log('Max reconnect attempts reached')
      ElMessage.error('WebSocket 连接失败，已达到最大重试次数')
      return
    }

    this.reconnectAttempts++
    console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.options.reconnectInterval || 5000)
  }
}

// 创建全局 WebSocket 服务实例
export const createWebSocketService = (options: WebSocketOptions): WebSocketService => {
  return new WebSocketService(options)
}

// 监测数据 WebSocket 服务
export const monitoringWebSocket = createWebSocketService({
  url: `ws://localhost:8000/api/v1/ws/monitoring`,
  onMessage: (message) => {
    // 全局消息处理逻辑
    console.log('Global monitoring message:', message)
  },
})

// 设备特定 WebSocket 服务工厂
export const createEquipmentWebSocket = (equipmentId: number): WebSocketService => {
  return createWebSocketService({
    url: `ws://localhost:8000/api/v1/ws/equipment/${equipmentId}`,
    onMessage: (message) => {
      console.log(`Equipment ${equipmentId} message:`, message)
    },
  })
}
