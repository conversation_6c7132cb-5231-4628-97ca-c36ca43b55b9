import asyncio
import random
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy.orm import Session
import logging

from app.core.database import SessionLocal
from app.models.equipment import Equipment as EquipmentModel
from app.models.monitoring_data import MonitoringData as MonitoringDataModel
from app.core.config import settings

logger = logging.getLogger(__name__)

class DataSimulator:
    """数据模拟器"""
    
    def __init__(self):
        self.is_running = False
        self.simulation_task: Optional[asyncio.Task] = None
    
    async def start_simulation(self):
        """开始数据模拟"""
        if self.is_running:
            logger.warning("数据模拟已在运行中")
            return
        
        self.is_running = True
        self.simulation_task = asyncio.create_task(self._simulation_loop())
        logger.info("数据模拟已启动")
    
    async def stop_simulation(self):
        """停止数据模拟"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.simulation_task:
            self.simulation_task.cancel()
            try:
                await self.simulation_task
            except asyncio.CancelledError:
                pass
        
        logger.info("数据模拟已停止")
    
    async def _simulation_loop(self):
        """模拟循环"""
        while self.is_running:
            try:
                await self._generate_monitoring_data()
                await asyncio.sleep(settings.SIMULATION_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"数据模拟出错: {e}")
                await asyncio.sleep(5)  # 出错后等待5秒再继续
    
    async def _generate_monitoring_data(self):
        """生成监测数据"""
        db = SessionLocal()
        try:
            # 获取所有激活的设备
            equipment_list = db.query(EquipmentModel).filter(
                EquipmentModel.is_active == True,
                EquipmentModel.status == "运行中"
            ).all()
            
            if not equipment_list:
                logger.warning("没有找到激活的设备")
                return
            
            # 为每个设备生成数据
            for equipment in equipment_list:
                monitoring_data = self._create_simulated_data(equipment)
                db.add(monitoring_data)
                
                # 更新设备电量（模拟电量消耗）
                if equipment.battery_level > 0:
                    equipment.battery_level = max(0, equipment.battery_level - random.uniform(0.1, 0.5))
                    
                    # 电量低时更新状态
                    if equipment.battery_level < 10:
                        equipment.status = "低电量"
                    elif equipment.battery_level == 0:
                        equipment.status = "离线"
            
            db.commit()
            logger.debug(f"为 {len(equipment_list)} 个设备生成了监测数据")
            
        except Exception as e:
            db.rollback()
            logger.error(f"生成监测数据时出错: {e}")
        finally:
            db.close()
    
    def _create_simulated_data(self, equipment: EquipmentModel) -> MonitoringDataModel:
        """创建模拟的监测数据"""
        # 基于时间和位置的变化模拟真实数据
        now = datetime.now()
        hour = now.hour
        
        # 模拟日夜温度变化
        base_temp = 20 + 10 * math.sin((hour - 6) * math.pi / 12)
        
        # 添加随机噪声和设备特定的偏移
        device_offset = hash(equipment.equipment_id) % 10 - 5
        
        return MonitoringDataModel(
            equipment_id=equipment.id,
            
            # 土壤数据
            soil_temperature=round(base_temp + device_offset + random.uniform(-2, 2), 1),
            soil_moisture=round(random.uniform(30, 80), 1),
            soil_ph=round(random.uniform(6.0, 7.5), 1),
            soil_conductivity=round(random.uniform(0.5, 2.0), 2),
            soil_nitrogen=round(random.uniform(10, 50), 1),
            soil_phosphorus=round(random.uniform(5, 25), 1),
            soil_potassium=round(random.uniform(15, 60), 1),
            
            # 环境数据
            ambient_temperature=round(base_temp + random.uniform(-3, 3), 1),
            ambient_humidity=round(random.uniform(40, 90), 1),
            light_intensity=round(max(0, 50000 * math.sin((hour - 6) * math.pi / 12) + random.uniform(-5000, 5000)), 0),
            uv_index=round(max(0, 8 * math.sin((hour - 6) * math.pi / 12) + random.uniform(-1, 1)), 1),
            wind_speed=round(random.uniform(0, 15), 1),
            wind_direction=round(random.uniform(0, 360), 0),
            atmospheric_pressure=round(random.uniform(1000, 1030), 1),
            rainfall=round(random.uniform(0, 5) if random.random() < 0.1 else 0, 1),
            
            # 设备状态
            monitor_battery=equipment.battery_level,
            signal_quality=round(random.uniform(70, 100), 1),
            
            # 数据质量
            data_quality="good" if random.random() > 0.05 else "warning",
            
            # 位置（可能与设备位置略有不同）
            longitude=equipment.longitude + random.uniform(-0.001, 0.001),
            latitude=equipment.latitude + random.uniform(-0.001, 0.001),
            altitude=equipment.altitude + random.uniform(-1, 1),
            
            measured_at=now
        )

# 导入math模块
import math

# 创建全局模拟器实例
data_simulator = DataSimulator()

async def start_data_simulation():
    """启动数据模拟"""
    if settings.ENABLE_DATA_SIMULATION:
        await data_simulator.start_simulation()

async def stop_data_simulation():
    """停止数据模拟"""
    await data_simulator.stop_simulation()
