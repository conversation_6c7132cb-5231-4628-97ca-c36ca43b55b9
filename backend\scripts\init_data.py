#!/usr/bin/env python3
"""
初始化数据脚本
用于创建示例设备和初始数据
"""

import sys
import os
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.database import SessionLocal, engine, Base
from app.models.equipment import Equipment
from app.models.monitoring_data import MonitoringData
from app.models.user import User
from app.core.security import get_password_hash

def create_default_users(db):
    """创建默认用户"""
    print("正在创建默认用户...")

    # 检查是否已有用户
    existing_user = db.query(User).first()
    if existing_user:
        print("用户已存在，跳过创建")
        return

    # 创建管理员用户
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        full_name="系统管理员",
        hashed_password=get_password_hash("admin123"),
        is_active=True,
        is_superuser=True,
        is_verified=True
    )
    db.add(admin_user)

    # 创建普通用户
    normal_user = User(
        username="user",
        email="<EMAIL>",
        full_name="普通用户",
        hashed_password=get_password_hash("user123"),
        is_active=True,
        is_superuser=False,
        is_verified=True
    )
    db.add(normal_user)

    db.commit()
    print("创建了默认用户:")
    print("- 管理员: admin / admin123")
    print("- 普通用户: user / user123")

def create_sample_equipment(db):
    """创建示例设备"""
    print("正在创建示例设备...")

    # 示例设备数据（基于前端项目中的GeoJSON数据）
    sample_equipment = [
        {
            "name": "设备1",
            "equipment_id": "FARM_001",
            "longitude": 116.3974,
            "latitude": 39.9093,
            "altitude": 50.0,
            "status": "运行中",
            "battery_level": 85.0,
            "signal_strength": 92.0,
            "model": "AgriSensor Pro",
            "manufacturer": "农业科技有限公司",
            "firmware_version": "v2.1.0",
            "installation_date": datetime.now() - timedelta(days=30)
        },
        {
            "name": "设备2",
            "equipment_id": "FARM_002",
            "longitude": 116.4074,
            "latitude": 39.9193,
            "altitude": 52.0,
            "status": "运行中",
            "battery_level": 78.0,
            "signal_strength": 88.0,
            "model": "AgriSensor Pro",
            "manufacturer": "农业科技有限公司",
            "firmware_version": "v2.1.0",
            "installation_date": datetime.now() - timedelta(days=25)
        },
        {
            "name": "设备3",
            "equipment_id": "FARM_003",
            "longitude": 116.3874,
            "latitude": 39.8993,
            "altitude": 48.0,
            "status": "运行中",
            "battery_level": 92.0,
            "signal_strength": 95.0,
            "model": "AgriSensor Lite",
            "manufacturer": "农业科技有限公司",
            "firmware_version": "v1.8.2",
            "installation_date": datetime.now() - timedelta(days=45)
        },
        {
            "name": "设备4",
            "equipment_id": "FARM_004",
            "longitude": 116.4174,
            "latitude": 39.9293,
            "altitude": 55.0,
            "status": "维护中",
            "battery_level": 15.0,
            "signal_strength": 65.0,
            "model": "AgriSensor Pro",
            "manufacturer": "农业科技有限公司",
            "firmware_version": "v2.0.5",
            "installation_date": datetime.now() - timedelta(days=60)
        },
        {
            "name": "设备5",
            "equipment_id": "FARM_005",
            "longitude": 116.3774,
            "latitude": 39.8893,
            "altitude": 46.0,
            "status": "运行中",
            "battery_level": 67.0,
            "signal_strength": 82.0,
            "model": "AgriSensor Lite",
            "manufacturer": "农业科技有限公司",
            "firmware_version": "v1.8.2",
            "installation_date": datetime.now() - timedelta(days=20)
        }
    ]

    equipment_objects = []
    for eq_data in sample_equipment:
        equipment = Equipment(**eq_data)
        db.add(equipment)
        equipment_objects.append(equipment)

    db.commit()

    # 刷新对象以获取ID
    for equipment in equipment_objects:
        db.refresh(equipment)

    print(f"创建了 {len(equipment_objects)} 个示例设备")
    return equipment_objects

def create_sample_monitoring_data(db, equipment_list):
    """创建示例监测数据"""
    print("正在创建示例监测数据...")

    data_count = 0

    # 为每个设备创建过去7天的数据
    for equipment in equipment_list:
        if equipment.status != "运行中":
            continue

        # 每天4次数据（每6小时一次）
        for day in range(7):
            for hour in [6, 12, 18, 24]:
                measured_time = datetime.now() - timedelta(days=day, hours=24-hour)

                # 基于时间模拟真实的环境变化
                base_temp = 20 + 8 * math.sin((hour - 6) * math.pi / 12)

                monitoring_data = MonitoringData(
                    equipment_id=equipment.id,

                    # 土壤数据
                    soil_temperature=round(base_temp + random.uniform(-3, 3), 1),
                    soil_moisture=round(random.uniform(35, 75), 1),
                    soil_ph=round(random.uniform(6.2, 7.3), 1),
                    soil_conductivity=round(random.uniform(0.8, 1.8), 2),
                    soil_nitrogen=round(random.uniform(15, 45), 1),
                    soil_phosphorus=round(random.uniform(8, 22), 1),
                    soil_potassium=round(random.uniform(20, 55), 1),

                    # 环境数据
                    ambient_temperature=round(base_temp + random.uniform(-2, 4), 1),
                    ambient_humidity=round(random.uniform(45, 85), 1),
                    light_intensity=round(max(0, 45000 * math.sin((hour - 6) * math.pi / 12) + random.uniform(-8000, 8000)), 0),
                    uv_index=round(max(0, 7 * math.sin((hour - 6) * math.pi / 12) + random.uniform(-1, 1)), 1),
                    wind_speed=round(random.uniform(0, 12), 1),
                    wind_direction=round(random.uniform(0, 360), 0),
                    atmospheric_pressure=round(random.uniform(1005, 1025), 1),
                    rainfall=round(random.uniform(0, 3) if random.random() < 0.15 else 0, 1),

                    # 设备状态
                    monitor_battery=equipment.battery_level + random.uniform(-5, 5),
                    signal_quality=equipment.signal_strength + random.uniform(-10, 5),

                    # 数据质量
                    data_quality="good" if random.random() > 0.08 else "warning",

                    # 位置
                    longitude=equipment.longitude + random.uniform(-0.0005, 0.0005),
                    latitude=equipment.latitude + random.uniform(-0.0005, 0.0005),
                    altitude=equipment.altitude + random.uniform(-0.5, 0.5),

                    measured_at=measured_time
                )

                db.add(monitoring_data)
                data_count += 1

    db.commit()
    print(f"创建了 {data_count} 条示例监测数据")

def main():
    """主函数"""
    print("开始初始化数据...")

    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成")

    # 创建数据库会话
    db = SessionLocal()

    try:
        # 检查是否已有数据
        existing_equipment = db.query(Equipment).first()
        if existing_equipment:
            print("数据库中已存在设备数据，跳过初始化")
            return

        # 创建示例数据
        create_default_users(db)
        equipment_list = create_sample_equipment(db)
        create_sample_monitoring_data(db, equipment_list)

        print("数据初始化完成！")
        print(f"- 设备数量: {len(equipment_list)}")
        print("- 可以通过以下URL访问API文档:")
        print("  - Swagger UI: http://localhost:8000/api/v1/docs")
        print("  - ReDoc: http://localhost:8000/api/v1/redoc")

    except Exception as e:
        print(f"初始化数据时出错: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import math
    main()
