// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    CameraPercentageChangeRate: typeof import('./src/components/jt-toolbar/dropdown/camera-percentage-change-rate/index.vue')['default']
    EarthLightSetting: typeof import('./src/components/jt-toolbar/dropdown/earth-light-setting/index.vue')['default']
    EarthSurfaceColorPicker: typeof import('./src/components/jt-toolbar/dropdown/earth-surface-color-picker/index.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElevationContourSetting: typeof import('./src/components/jt-toolbar/dropdown/elevation-contour-setting/index.vue')['default']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ImageryLayerOperate: typeof import('./src/components/jt-browser-panel/imagery/imagery-layer-operate.vue')['default']
    ImageryManager: typeof import('./src/components/jt-browser-panel/imagery/imagery-manager.vue')['default']
    ImagerySelect: typeof import('./src/components/jt-browser-panel/imagery/imagery-select.vue')['default']
    ImagerySplitPosition: typeof import('./src/components/jt-toolbar/dropdown/imagery-split-position/index.vue')['default']
    JtBrowserPanel: typeof import('./src/components/jt-browser-panel/index.vue')['default']
    JtCameraSetting: typeof import('./src/components/jt-global-register/components/jt-camera-setting/index.vue')['default']
    JtCartographicLimitRectangle: typeof import('./src/components/jt-global-register/components/jt-cartographic-limit-rectangle/index.vue')['default']
    JtCesiumVue: typeof import('./src/components/jt-cesium-vue/index.vue')['default']
    JtDraggableResizable: typeof import('./src/components/jt-draggable-resizable/index.vue')['default']
    JtFloodAnalysis: typeof import('./src/components/jt-global-register/components/jt-flood-analysis/index.vue')['default']
    JtGroup: typeof import('./src/components/jt-toolbar/components/jt-group.vue')['default']
    JtGroups: typeof import('./src/components/jt-toolbar/components/jt-groups.vue')['default']
    JtIcon: typeof import('./src/components/jt-global-register/components/jt-icon/index.vue')['default']
    JtImageryLayerCorrectOffset: typeof import('./src/components/jt-global-register/components/jt-imagery-layer-correct-offset/index.vue')['default']
    JtItem: typeof import('./src/components/jt-toolbar/components/jt-item.vue')['default']
    JtItems: typeof import('./src/components/jt-toolbar/components/jt-items.vue')['default']
    JtLocationbar: typeof import('./src/components/jt-locationbar/index.vue')['default']
    JtMainAreaDialog: typeof import('./src/components/jt-global-register/components/jt-main-area-dialog/index.vue')['default']
    JtMenus: typeof import('./src/components/jt-toolbar/components/jt-menus.vue')['default']
    JtOverlay: typeof import('./src/components/jt-overlay/index.vue')['default']
    JtPrimitiveClippingPlane: typeof import('./src/components/jt-global-register/components/jt-primitive-clipping-plane/index.vue')['default']
    JtSetting: typeof import('./src/components/jt-global-register/components/jt-setting/index.vue')['default']
    JtSideCollapse: typeof import('./src/components/jt-side-collapse/index.vue')['default']
    JtTerrainSampleChart: typeof import('./src/components/jt-terrain-sample-chart/index.vue')['default']
    JtToolbar: typeof import('./src/components/jt-toolbar/index.vue')['default']
    PrimitiveManager: typeof import('./src/components/jt-browser-panel/primitive/primitive-manager.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectViewMode: typeof import('./src/components/jt-toolbar/dropdown/select-view-mode/index.vue')['default']
    TerrainManager: typeof import('./src/components/jt-browser-panel/terrain/terrain-manager.vue')['default']
    TerrainSetting: typeof import('./src/components/jt-browser-panel/terrain/terrain-setting.vue')['default']
    Vue3DraggableResizable: typeof import('./src/components/jt-draggable-resizable/vue3-draggable-resizable.vue')['default']
  }
}
