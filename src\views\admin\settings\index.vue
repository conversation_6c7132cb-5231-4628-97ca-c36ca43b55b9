<template>
  <div class="settings-management">
    <el-row :gutter="20">
      <!-- 系统配置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统配置</span>
              <el-button
                type="primary"
                size="small"
                :loading="systemLoading"
                @click="saveSystemSettings"
              >
                保存
              </el-button>
            </div>
          </template>
          
          <el-form :model="systemSettings" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="systemSettings.system_name" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统版本">
              <el-input v-model="systemSettings.system_version" placeholder="请输入系统版本" />
            </el-form-item>
            
            <el-form-item label="系统描述">
              <el-input
                v-model="systemSettings.system_description"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            
            <el-form-item label="维护模式">
              <el-switch
                v-model="systemSettings.maintenance_mode"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="用户注册">
              <el-switch
                v-model="systemSettings.allow_registration"
                active-text="允许"
                inactive-text="禁止"
              />
            </el-form-item>
            
            <el-form-item label="邮箱验证">
              <el-switch
                v-model="systemSettings.require_email_verification"
                active-text="必须"
                inactive-text="可选"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 数据配置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>数据配置</span>
              <el-button
                type="primary"
                size="small"
                :loading="dataLoading"
                @click="saveDataSettings"
              >
                保存
              </el-button>
            </div>
          </template>
          
          <el-form :model="dataSettings" label-width="120px">
            <el-form-item label="数据保留天数">
              <el-input-number
                v-model="dataSettings.data_retention_days"
                :min="1"
                :max="3650"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="自动备份">
              <el-switch
                v-model="dataSettings.auto_backup"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="备份间隔(小时)">
              <el-input-number
                v-model="dataSettings.backup_interval_hours"
                :min="1"
                :max="168"
                :disabled="!dataSettings.auto_backup"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="数据压缩">
              <el-switch
                v-model="dataSettings.enable_compression"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="数据加密">
              <el-switch
                v-model="dataSettings.enable_encryption"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 通知配置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>通知配置</span>
              <el-button
                type="primary"
                size="small"
                :loading="notificationLoading"
                @click="saveNotificationSettings"
              >
                保存
              </el-button>
            </div>
          </template>
          
          <el-form :model="notificationSettings" label-width="120px">
            <el-form-item label="邮件通知">
              <el-switch
                v-model="notificationSettings.email_enabled"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="SMTP服务器">
              <el-input
                v-model="notificationSettings.smtp_server"
                placeholder="请输入SMTP服务器地址"
                :disabled="!notificationSettings.email_enabled"
              />
            </el-form-item>
            
            <el-form-item label="SMTP端口">
              <el-input-number
                v-model="notificationSettings.smtp_port"
                :min="1"
                :max="65535"
                :disabled="!notificationSettings.email_enabled"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="发件人邮箱">
              <el-input
                v-model="notificationSettings.sender_email"
                placeholder="请输入发件人邮箱"
                :disabled="!notificationSettings.email_enabled"
              />
            </el-form-item>
            
            <el-form-item label="短信通知">
              <el-switch
                v-model="notificationSettings.sms_enabled"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="推送通知">
              <el-switch
                v-model="notificationSettings.push_enabled"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <!-- 监控配置 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>监控配置</span>
              <el-button
                type="primary"
                size="small"
                :loading="monitoringLoading"
                @click="saveMonitoringSettings"
              >
                保存
              </el-button>
            </div>
          </template>
          
          <el-form :model="monitoringSettings" label-width="120px">
            <el-form-item label="数据采集间隔">
              <el-select v-model="monitoringSettings.collection_interval" style="width: 100%">
                <el-option label="1分钟" :value="60" />
                <el-option label="5分钟" :value="300" />
                <el-option label="10分钟" :value="600" />
                <el-option label="30分钟" :value="1800" />
                <el-option label="1小时" :value="3600" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="异常检测">
              <el-switch
                v-model="monitoringSettings.anomaly_detection"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="告警阈值">
              <el-input-number
                v-model="monitoringSettings.alert_threshold"
                :min="0"
                :max="100"
                :precision="1"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item label="设备离线告警">
              <el-switch
                v-model="monitoringSettings.offline_alert"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="低电量告警">
              <el-switch
                v-model="monitoringSettings.low_battery_alert"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>
            
            <el-form-item label="电量告警阈值">
              <el-input-number
                v-model="monitoringSettings.battery_threshold"
                :min="0"
                :max="100"
                :disabled="!monitoringSettings.low_battery_alert"
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 系统信息 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          
          <el-descriptions :column="3" border>
            <el-descriptions-item label="系统版本">
              {{ systemInfo.version }}
            </el-descriptions-item>
            <el-descriptions-item label="运行时间">
              {{ systemInfo.uptime }}
            </el-descriptions-item>
            <el-descriptions-item label="数据库版本">
              {{ systemInfo.database_version }}
            </el-descriptions-item>
            <el-descriptions-item label="CPU使用率">
              <el-progress :percentage="systemInfo.cpu_usage" :color="getProgressColor(systemInfo.cpu_usage)" />
            </el-descriptions-item>
            <el-descriptions-item label="内存使用率">
              <el-progress :percentage="systemInfo.memory_usage" :color="getProgressColor(systemInfo.memory_usage)" />
            </el-descriptions-item>
            <el-descriptions-item label="磁盘使用率">
              <el-progress :percentage="systemInfo.disk_usage" :color="getProgressColor(systemInfo.disk_usage)" />
            </el-descriptions-item>
            <el-descriptions-item label="活跃用户">
              {{ systemInfo.active_users }}
            </el-descriptions-item>
            <el-descriptions-item label="在线设备">
              {{ systemInfo.online_devices }}
            </el-descriptions-item>
            <el-descriptions-item label="今日数据量">
              {{ systemInfo.today_data_count }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 加载状态
const systemLoading = ref(false)
const dataLoading = ref(false)
const notificationLoading = ref(false)
const monitoringLoading = ref(false)

// 系统配置
const systemSettings = reactive({
  system_name: '农业监测系统',
  system_version: '1.0.0',
  system_description: '基于Vue3和Cesium的农业监测管理系统',
  maintenance_mode: false,
  allow_registration: true,
  require_email_verification: false
})

// 数据配置
const dataSettings = reactive({
  data_retention_days: 365,
  auto_backup: true,
  backup_interval_hours: 24,
  enable_compression: true,
  enable_encryption: false
})

// 通知配置
const notificationSettings = reactive({
  email_enabled: true,
  smtp_server: 'smtp.example.com',
  smtp_port: 587,
  sender_email: '<EMAIL>',
  sms_enabled: false,
  push_enabled: true
})

// 监控配置
const monitoringSettings = reactive({
  collection_interval: 300,
  anomaly_detection: true,
  alert_threshold: 80.0,
  offline_alert: true,
  low_battery_alert: true,
  battery_threshold: 20
})

// 系统信息
const systemInfo = reactive({
  version: '1.0.0',
  uptime: '7天 12小时 30分钟',
  database_version: 'PostgreSQL 15.0',
  cpu_usage: 25,
  memory_usage: 45,
  disk_usage: 60,
  active_users: 12,
  online_devices: 8,
  today_data_count: 1250
})

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 保存系统设置
const saveSystemSettings = async () => {
  systemLoading.value = true
  try {
    // 这里应该调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    ElMessage.error('系统配置保存失败')
  } finally {
    systemLoading.value = false
  }
}

// 保存数据设置
const saveDataSettings = async () => {
  dataLoading.value = true
  try {
    // 这里应该调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据配置保存成功')
  } catch (error) {
    ElMessage.error('数据配置保存失败')
  } finally {
    dataLoading.value = false
  }
}

// 保存通知设置
const saveNotificationSettings = async () => {
  notificationLoading.value = true
  try {
    // 这里应该调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('通知配置保存成功')
  } catch (error) {
    ElMessage.error('通知配置保存失败')
  } finally {
    notificationLoading.value = false
  }
}

// 保存监控设置
const saveMonitoringSettings = async () => {
  monitoringLoading.value = true
  try {
    // 这里应该调用保存API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('监控配置保存成功')
  } catch (error) {
    ElMessage.error('监控配置保存失败')
  } finally {
    monitoringLoading.value = false
  }
}

// 加载配置
const loadSettings = async () => {
  try {
    // 这里应该调用获取配置的API
    // 模拟加载配置
  } catch (error) {
    ElMessage.error('加载配置失败')
  }
}

// 组件挂载
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-progress) {
  width: 100%;
}
</style>
